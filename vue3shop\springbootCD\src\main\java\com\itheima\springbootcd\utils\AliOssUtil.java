package com.itheima.springbootcd.utils;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyuncs.exceptions.ClientException;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.UUID;
public class AliOssUtil {
    // 阿里云OSS配置
    private static final String ENDPOINT = "https://oss-cn-hangzhou.aliyuncs.com";
    private static final String BUCKET_NAME = "hmleadnews0225";
    private static final String ACCESS_KEY_ID = "LTAI5tKU6DpqYxT8WQPydb4s";  // 替换为您的 AccessKeyId
    private static final String ACCESS_KEY_SECRET = "******************************";  // 替换为您的 AccessKeySecret

    /**
     * 实现图片上传
     */
    public static String upload(MultipartFile image) throws IOException, ClientException {
        // 获取原始文件名
        String originalFilename = image.getOriginalFilename();
        // 生成新文件名
        String objectName = UUID.randomUUID().toString() +
                originalFilename.substring(originalFilename.lastIndexOf("."));

        // 创建OSSClient实例
        OSS ossClient = new OSSClientBuilder().build(ENDPOINT, ACCESS_KEY_ID, ACCESS_KEY_SECRET);

        try {
            // 获取文件输入流
            InputStream inputStream = image.getInputStream();
            // 创建PutObjectRequest对象
            PutObjectRequest putObjectRequest = new PutObjectRequest(BUCKET_NAME, objectName, inputStream);
            // 上传文件
            ossClient.putObject(putObjectRequest);

            // 生成永久访问URL
            String url = "https://" + BUCKET_NAME + "." + ENDPOINT.replace("https://", "") + "/" + objectName;

            return url;
        } catch (Exception e) {
            throw new RuntimeException("文件上传失败", e);
        } finally {
            // 关闭OSSClient
            ossClient.shutdown();
        }
    }
}