<template>
  <div>
    <RtHeader />
    <div class="header-gap">
      <RtInfo />
    </div>
    <div class="order-list-row">
      <RtOrder />
      <RtList />
    </div>
    <RtTabBar />
  </div>
</template>

<script setup>
import RtInfo from './RtInfo.vue'
import RtOrder from './RtOrder.vue'
import RtList from './RtList.vue'
</script>

<style scoped>
.header-gap {
  margin-top: 20px;
}
.order-list-row {
  display: flex;
  flex-direction: row;
  gap: 24px;
  margin-top: 20px;
}
.order-list-row > * {
  flex: 1;
}
</style>
