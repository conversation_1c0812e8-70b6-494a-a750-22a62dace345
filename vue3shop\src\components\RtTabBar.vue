<template>
  <div class="tab-bar">
    <div
      v-for="item in tabList"
      :key="item.name"
      class="tab-bar-item"
      :class="{ active: activeTab === item.name }"
      @click="changeTab(item.name)"
    >
      <img :src="activeTab === item.name ? item.checkedIcon : item.icon" />
      <span>{{ item.label }}</span>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import fenleiChecked from '@/assets/images/tabBar/分类-选中.svg'
import fenlei from '@/assets/images/tabBar/分类.svg'
import shouyeChecked from '@/assets/images/tabBar/首页-选中.svg'
import shouye from '@/assets/images/tabBar/首页.svg'
import wodeChecked from '@/assets/images/tabBar/我的-选中.svg'
import wode from '@/assets/images/tabBar/我的.svg'
import dongtaiChecked from '@/assets/images/tabBar/ZS动态-选中.svg'
import dongtai from '@/assets/images/tabBar/ZS动态1.svg'
import shangpin from '@/assets/images/tabBar/商品.svg'
import shangpinChecked from '@/assets/images/tabBar/商品-选中.svg'

const router = useRouter()
const route = useRoute()

const tabList = [
  {
    name: 'home',
    label: '首页',
    icon: shouye,
    checkedIcon: shouyeChecked,
    path: '/'
  },
  {
    name: 'category',
    label: '分类',
    icon: fenlei,
    checkedIcon: fenleiChecked,
    path: '/category'
  },
  {
    name: 'dynamic',
    label: '动态',
    icon: dongtai,
    checkedIcon: dongtaiChecked,
    path: '/dynamic'
  },
  {
    name: 'mine',
    label: '我的',
    icon: wode,
    checkedIcon: wodeChecked,
    path: '/user'
  },
  {
    name: 'product',
    label: '商品',
    icon: shangpin,
    checkedIcon: shangpinChecked,
    path: '/product/manage'
  }
]

const activeTab = computed(() => {
  const current = tabList.find(tab => tab.path === route.path)
  return current ? current.name : ''
})

function changeTab(name) {
  const tab = tabList.find(t => t.name === name)
  if (tab && tab.path && route.path !== tab.path) {
    router.push(tab.path)
  }
}
</script>

<style scoped>
.tab-bar {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  height: 60px;
  background: #fff;
  border-top: 1px solid #eee;
  box-shadow: 0 -2px 8px #f0f0f0;
}
.tab-bar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  padding: 6px 0;
  color: #666;
  font-size: 13px;
  cursor: pointer;
  transition: color 0.2s;
}
.tab-bar-item img {
  width: 28px;
  height: 28px;
  margin-bottom: 2px;
}
.tab-bar-item.active,
.tab-bar-item:hover {
  color: #2193b0;
}
.tab-bar-item.active span {
  font-weight: bold;
}
</style>