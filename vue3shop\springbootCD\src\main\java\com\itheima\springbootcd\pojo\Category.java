package com.itheima.springbootcd.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.groups.Default;
import java.io.Serializable;
import java.time.LocalDateTime;
/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@Data
public class Category implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "category_id", type = IdType.AUTO)
    @NotNull(groups = Update.class)
    private Integer categoryId;

    @TableField("category_name")
    @NotEmpty
    private String categoryName;

    @TableField("description")
    @NotEmpty
    private String description;

    @TableField("create_user")
    private Integer createUser;

    @TableField("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @TableField("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;
    //分组校验->解决添加时不需要添加id，因为是自增的,更新时候需要id
    //如果说某个校验项没有指定分组，就归属于Default分组
    //分组之间可以继承,A extends B 那么A中拥有B中所有的校验项
    public interface Add extends Default {

    }
    public interface Update extends Default{

    }
}
