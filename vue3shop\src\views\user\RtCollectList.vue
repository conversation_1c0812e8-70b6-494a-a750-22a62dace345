<template>
  <div class="collect-container">
    <div class="page-header">
      <h2>我的收藏</h2>
    </div>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
    </div>
    
    <!-- 收藏列表 -->
    <div v-else-if="collectList && collectList.length > 0" class="collect-list">
      <el-row :gutter="20">
        <el-col :span="6" v-for="item in collectList" :key="item.id">
          <el-card class="collect-item" :body-style="{ padding: '0px' }">
            <div class="product-image" @click="goToDetail(item.productId)">
              <el-image :src="item.productImage" fit="cover" />
            </div>
            <div class="product-info">
              <h3 class="product-name" @click="goToDetail(item.productId)">{{ item.productName }}</h3>
              <div class="product-price">¥{{ item.productPrice }}</div>
              <div class="product-actions">
                <el-button type="danger" size="small" @click="cancelCollect(item.id)">
                  取消收藏
                </el-button>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[8, 16, 24, 32]"
          :total="total"
          layout="total, sizes, prev, pager, next"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    
    <!-- 空状态 -->
    <el-empty v-else description="暂无收藏商品" />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import request from '@/utils/request'

const router = useRouter()

// 分页相关数据
const currentPage = ref(1)
const pageSize = ref(8)
const total = ref(0)

// 收藏列表数据
const collectList = ref([])
const loading = ref(false)

// 获取收藏列表
const getCollectList = async () => {
  loading.value = true
  try {
    const response = await request.get('/collect/list', {
      params: {
        page: currentPage.value,
        pageSize: pageSize.value
      }
    })
    
    if (response.code === 0) {
      collectList.value = response.data
      total.value = response.total
    } else {
      ElMessage.error(response.message || '获取收藏列表失败')
    }
  } catch (error) {
    console.error('获取收藏列表失败:', error)
    ElMessage.error('获取收藏列表失败')
  } finally {
    loading.value = false
  }
}

// 取消收藏
const cancelCollect = async (id) => {
  try {
    await ElMessageBox.confirm('确定要取消收藏该商品吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await request.delete(`/collect/${id}`)
    
    if (response.code === 0) {
      ElMessage.success('取消收藏成功')
      // 重新获取列表
      getCollectList()
    } else {
      ElMessage.error(response.message || '取消收藏失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消收藏失败:', error)
      ElMessage.error('取消收藏失败')
    }
  }
}

// 跳转到商品详情
const goToDetail = (productId) => {
  router.push(`/product/${productId}`)
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  getCollectList()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  getCollectList()
}

// 页面加载时获取数据
onMounted(() => {
  getCollectList()
})
</script>

<style lang="scss" scoped>
.collect-container {
  padding: 20px;
  
  .page-header {
    margin-bottom: 20px;
    
    h2 {
      font-size: 24px;
      color: #303133;
      margin: 0;
    }
  }
  
  .loading-container {
    padding: 20px;
  }
  
  .collect-list {
    .collect-item {
      margin-bottom: 20px;
      transition: all 0.3s;
      
      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
      }
      
      .product-image {
        height: 200px;
        overflow: hidden;
        cursor: pointer;
        
        .el-image {
          width: 100%;
          height: 100%;
        }
      }
      
      .product-info {
        padding: 14px;
        
        .product-name {
          margin: 0 0 10px;
          font-size: 16px;
          color: #303133;
          cursor: pointer;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          
          &:hover {
            color: var(--el-color-primary);
          }
        }
        
        .product-price {
          color: #f56c6c;
          font-size: 18px;
          font-weight: bold;
          margin-bottom: 10px;
        }
        
        .product-actions {
          display: flex;
          justify-content: flex-end;
        }
      }
    }
  }
  
  .pagination {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
}
</style>