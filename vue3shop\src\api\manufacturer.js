import request from '@/utils/request'

/**
 * 获取生产商列表
 */
export const manufacturerListService = () => {
  return request.get('/manufacturer/list')
}

/**
 * 获取生产商详情
 * @param {number} id 生产商ID
 */
export const manufacturerDetailService = (id) => {
  return request.get(`/manufacturer/${id}/detail`)
}

/**
 * 添加生产商（管理员功能）
 * @param {object} manufacturer 生产商信息
 */
export const manufacturerAddService = (manufacturer) => {
  return request.post('/manufacturer/add', manufacturer)
}

/**
 * 更新生产商信息
 * @param {object} manufacturer 生产商信息
 */
export const manufacturerUpdateService = (manufacturer) => {
  return request.put('/manufacturer/update', manufacturer)
}

/**
 * 删除生产商（管理员功能）
 * @param {number} id 生产商ID
 */
export const manufacturerDeleteService = (id) => {
  return request.delete(`/manufacturer/${id}`)
}

/**
 * 获取当前用户的生产商信息（生产商用户专用）
 */
export const getMyManufacturerService = () => {
  return request.get('/manufacturer/my')
}
