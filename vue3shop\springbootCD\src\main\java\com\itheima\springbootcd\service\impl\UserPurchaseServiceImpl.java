package com.itheima.springbootcd.service.impl;

import com.itheima.springbootcd.pojo.UserPurchase;
import com.itheima.springbootcd.mapper.UserPurchaseMapper;
import com.itheima.springbootcd.service.IUserPurchaseService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@Service
public class UserPurchaseServiceImpl extends ServiceImpl<UserPurchaseMapper, UserPurchase> implements IUserPurchaseService {

}
