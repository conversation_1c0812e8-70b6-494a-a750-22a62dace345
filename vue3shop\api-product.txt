import request from '@/utils/request'
import { useUserStore } from '@/stores/user'

// 分类列表
export const productCategoryListService = () => {
  const userStore = useUserStore()
  return request.get('/category', {
    headers: {
      'Authorization': userStore.token
    }
  })
}

// 添加分类
export const productCategoryAddService = (categoryData) => {
  const userStore = useUserStore()
  return request.post('/category', categoryData, {
    headers: {
      'Authorization': userStore.token
    }
  })
}

// 提供调用获取商品列表接口的函数
export const productListService = () => {
  return request({
    url: '/product/list',
    method: 'get',
    headers: {
      'Content-Type': 'application/json' // 对于GET请求，通常使用application/json或不指定Content-Type
    }
  })
}

// 提供调用获取商品详情接口的函数
export const productDetailService = (productId) => {
  return request({
    url: `/product/selectById/${productId}`,
    method: 'get',
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 产品分类修改
export const productCategoryUpdateService = (categoryData) => {
  const userStore = useUserStore()
  return request.put('/category', categoryData, {
    headers: {
      'Authorization': userStore.token
    }
  })
}

// 产品分类删除
export const productCategoryDeleteService = (categoryId) => {
  return request.post('/category/delete', { categoryId })
}

// 添加商品
export const productAddService = (productData) => {
  const userStore = useUserStore()
  return request.post('/product/add', productData, {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': userStore.token
    }
  })
}

// 更新商品
export const productUpdateService = (productId, productData) => {
  const userStore = useUserStore()
  return request.put(`/product/update/${productId}`, productData, { // 使用 PUT 请求
    headers: {
      'Content-Type': 'application/json',
      'Authorization': userStore.token
    }
  })
}

// 删除商品
export const productDeleteService = (productId) => {
  const userStore = useUserStore()
  return request.delete(`/product/delete/${productId}`, {}, { 
    headers: {
      'Content-Type': 'application/json',
      'Authorization': userStore.token
    }
  })
}
