package com.itheima.springbootcd.service;

import com.itheima.springbootcd.pojo.Cart;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
public interface ICartService extends IService<Cart> {

    boolean addToCart(Cart cart);

    boolean updateQuantity(Integer cartId, Integer quantity);

    boolean removeCartItem(Integer cartId);

    List<Cart> getCartList(Long userId);

    boolean removeCartItem(Long cartId);

    boolean clearCart(Long userId);
}
