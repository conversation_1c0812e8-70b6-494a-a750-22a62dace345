package com.itheima.springbootcd.service.impl;



import com.itheima.springbootcd.mapper.CollectMapper;
import com.itheima.springbootcd.pojo.Collect;
import com.itheima.springbootcd.service.CollectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CollectServiceImpl implements CollectService {

    @Autowired
    private CollectMapper collectMapper;

    @Override
    public void add(Collect collect) {
        collectMapper.add(collect);
    }

    @Override
    public void delete(Integer id, Integer userId) {
        collectMapper.delete(id, userId);
    }

    @Override
    public List<Collect> list(Integer userId, Integer page, Integer pageSize) {
        Integer offset = (page - 1) * pageSize;
        return collectMapper.list(userId, offset, pageSize);
    }

    @Override
    public Integer count(Integer userId) {
        return collectMapper.count(userId);
    }

    @Override
    public boolean checkCollect(Integer userId, Integer productId) {
        return collectMapper.checkCollect(userId, productId) > 0;
    }
}