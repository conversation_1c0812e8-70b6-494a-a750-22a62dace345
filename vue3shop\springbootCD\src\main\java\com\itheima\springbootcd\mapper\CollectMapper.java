package com.itheima.springbootcd.mapper;


import com.itheima.springbootcd.pojo.Collect;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface CollectMapper {

    @Insert("INSERT INTO collect(user_id, product_id) VALUES(#{userId}, #{productId})")
    void add(Collect collect);

    @Delete("DELETE FROM collect WHERE id = #{id} AND user_id = #{userId}")
    void delete(Integer id, Integer userId);

    @Select("SELECT c.*, p.product_name as product_name, p.image_path as product_image, p.price as product_price " +
            "FROM collect c " +
            "LEFT JOIN product p ON c.product_id = p.product_id " +
            "WHERE c.user_id = #{userId} " +
            "ORDER BY c.create_time DESC " +
            "LIMIT #{offset}, #{pageSize}")
    List<Collect> list(Integer userId, Integer offset, Integer pageSize);

    @Select("SELECT COUNT(*) FROM collect WHERE user_id = #{userId}")
    Integer count(Integer userId);

    @Select("SELECT COUNT(*) FROM collect WHERE user_id = #{userId} AND product_id = #{productId}")
    Integer checkCollect(Integer userId, Integer productId);
}
