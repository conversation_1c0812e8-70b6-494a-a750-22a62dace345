package com.itheima.springbootcd;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import java.util.Arrays;

@SpringBootApplication
public class SpringbootCdApplication {
    public static void main(String[] args) {
        SpringApplication.run(SpringbootCdApplication.class, args);
    }

    private CorsConfiguration buildCorsConfig() {
        CorsConfiguration config = new CorsConfiguration();
        // 允许的域名（根据环境动态配置）
        config.setAllowedOrigins(Arrays.asList(
                "http://localhost:8081",       // 前端开发环境
                "http://localhost:8080",       // 后端开发环境
                "http://127.0.0.1:8081",       // 前端开发环境（IP）
                "http://127.0.0.1:8080"        // 后端开发环境（IP）
        ));
        // 允许的请求头
        config.setAllowedHeaders(Arrays.asList(
                "Authorization",
                "Content-Type",
                "X-Requested-With",
                "Accept",
                "Origin",
                "Access-Control-Request-Method",
                "Access-Control-Request-Headers"
        ));
        // 允许的 HTTP 方法
        config.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"));
        // 允许携带凭证（如 Cookie）
        config.setAllowCredentials(true);
        // 预检请求缓存时间
        config.setMaxAge(3600L);
        return config;
    }

    @Bean
    public CorsFilter corsFilter() {
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", buildCorsConfig());
        return new CorsFilter(source);
    }
}
