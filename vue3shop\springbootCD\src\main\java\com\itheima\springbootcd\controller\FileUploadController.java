package com.itheima.springbootcd.controller;

import com.aliyuncs.exceptions.ClientException;
import com.itheima.springbootcd.pojo.Result;
import com.itheima.springbootcd.utils.AliOssUtil;
import org.springframework.web.multipart.MultipartFile;
import java.io.IOException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@RestController
public class FileUploadController {
    @PostMapping("/upload")
    public Result<String> upload(@RequestParam("image") MultipartFile image) {
        try {
            String url = AliOssUtil.upload(image);
            return Result.success(url);
        } catch (IOException e) {
            return Result.error("文件上传失败");
        } catch (ClientException e) {
            throw new RuntimeException(e);
        }
    }
}
