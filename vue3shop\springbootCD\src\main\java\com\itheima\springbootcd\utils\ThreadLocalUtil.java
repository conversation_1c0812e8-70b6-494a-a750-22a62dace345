package com.itheima.springbootcd.utils;

import java.util.HashMap;
import java.util.Map;
//ThreadLocal是线程独立的，使用ThreadLocal方便在各个线程设置一个存储空间用来存放数据，而不必在每个方法中都显式地传递用户信息参数。 大部分使用场景是将用户信息存储到ThreaLocal。
/**
 * ThreadLocal 工具类
 */
@SuppressWarnings("all")
public class ThreadLocalUtil {
    //提供ThreadLocal对象,
    private static final ThreadLocal THREAD_LOCAL = new ThreadLocal();

    //根据键获取值
    public static <T> T get(){
        return (T) THREAD_LOCAL.get();
    }
	
    //存储键值对
    public static void set(Object value){
        THREAD_LOCAL.set(value);
    }


    //清除ThreadLocal 防止内存泄漏
    public static void remove(){
        THREAD_LOCAL.remove();
    }
}
