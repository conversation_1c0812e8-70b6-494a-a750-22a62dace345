<template>
  <div class="user-info-card">
    <div class="user-info-header">
      <img :src="user.navatar" class="navatar" @click="goToAvatar" />
      <div class="user-info-main">
        <div class="user_name">{{ user.name }}</div>
        <div class="user_span">
          <span @click="goToBasicInfo" class="user-action">基本资料</span>
          <span @click="changeAccount" class="user-action">切换账号</span>
          <span @click="handleLogout" class="user-action">退出登录</span>
        </div>
      </div>
    </div>
    <div class="user-data-list">
      <div v-for="item in dataList" :key="item.id" class="data-item">
        <img :src="item.src" class="data-img" />
        <div class="data-text">{{ item.text }}</div>
      </div>
    </div>
    <div class="show-purchase" @click="goToOrder">
      <div class="purchase-item">
        <div>{{ purchase.waitToPay }}</div>
        <span>待付款</span>
      </div>
      <div class="purchase-item">
        <div>{{ purchase.waitToGet }}</div>
        <span>待收货</span>
      </div>
      <div class="purchase-item">
        <div>{{ purchase.waitToComment }}</div>
        <span>待评价</span>
      </div>
      <div class="purchase-item">
        <div>{{ purchase.waitToSetback }}</div>
        <span>退货退款</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import navatar from '@/assets/images/my/navatar.jpg'
import jilu from '@/assets/images/my/浏览记录.svg'
import collect from '@/assets/images/my/我的收藏.svg'
import guanzhu from '@/assets/images/my/店铺.svg'
import {userInfoService} from '@/api/user'
import useUserInfoStore from '@/stores/userInfo'
import { ElMessage, ElMessageBox } from 'element-plus'

const router = useRouter()
const userStore = useUserStore()

const user = reactive({
  navatar: navatar,
  name: '',
})
const purchase = reactive({
  waitToPay: 0,
  waitToGet: 0,
  waitToComment: 0,
  waitToSetback: 0
})

const dataList = [
  { id: 1, src: jilu, text: '浏览记录' },
  { id: 2, src: collect, text: '商品收藏' },
  { id: 3, src: guanzhu, text: '店铺关注' },
]

const userInfoStore = useUserInfoStore()
const getUserInfo = async() => {
  try {
    //调用接口
    let result = await userInfoService();
    console.log('用户信息返回数据:', result.data)
    if (result.code === 0 && result.data) {
      //数据存储到pinia中
      userInfoStore.setInfo(result.data)
      //更新本地显示数据
      user.name = result.data.username || '未设置用户名'
      
      // 处理头像URL
      let avatarUrl = result.data.avatar
      console.log('头像URL:', avatarUrl)
      
      // 直接使用阿里云OSS的URL
      if (avatarUrl && avatarUrl.startsWith('http')) {
        user.navatar = avatarUrl
      } else {
        user.navatar = navatar
      }
      
      console.log('最终使用的头像地址:', user.navatar)
      
      //更新订单相关数据
      purchase.waitToPay = result.data.waitToPay || 0
      purchase.waitToGet = result.data.waitToGet || 0
      purchase.waitToComment = result.data.waitToComment || 0
      purchase.waitToSetback = result.data.waitToSetback || 0
    } else {
      ElMessage.error('获取用户信息失败')
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    ElMessage.error('获取用户信息失败')
  }
}

// 组件挂载时获取用户信息
onMounted(() => {
  getUserInfo()
})

function changeAccount() {
  // 切换账号逻辑
  router.push('/login')
}

function handleLogout() {
  ElMessageBox.confirm(
    '确定要退出登录吗？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      // 清除用户信息
      userInfoStore.removeInfo()
      // 清除 token 和用户信息
      userStore.logout()
      // 清除本地存储
      localStorage.removeItem('user-store')
      localStorage.removeItem('userInfo-store')
      // 跳转到登录页
      router.push('/login')
      ElMessage.success('已退出登录')
    })
    .catch(() => {
      ElMessage.info('已取消退出')
    })
}

function goToOrder() {
  router.push('/order/list')
}

// 跳转到基本资料页面
function goToBasicInfo() {
  router.push('/user/info') // 假设 UserInfo.vue 对应的路由是 '/user/info'
}

// 跳转到头像设置页面
function goToAvatar() {
  router.push('/user/avatar')
}
</script>

<style scoped>
.user-info-card {
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 2px 12px #e3f2fd;
  padding: 22px 18px 18px 18px;
  margin-bottom: 18px;
  min-width: 0;
}
.user-info-header {
  display: flex;
  align-items: center;
  gap: 18px;
  margin-bottom: 18px;
}
.navatar {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  border: 2px solid #2193b0;
  box-shadow: 0 2px 8px #b2ebf2;
  object-fit: cover;
  background: #f0f7fa;
}
.user-info-main {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1;
}
.user_name {
  font-size: 20px;
  font-weight: bold;
  color: #1565c0;
  margin-bottom: 8px;
  letter-spacing: 1px;
}
.user_span {
  display: flex;
  flex-direction: row;
  gap: 18px;
}
.user-action {
  font-size: 14px;
  color: #2193b0;
  cursor: pointer;
  padding: 0 8px;
  transition: color 0.2s;
  border-right: 1px solid #e0e0e0;
}
.user-action:last-child {
  border-right: none;
}
.user-action:hover {
  color: #e2231a;
}
.user-data-list {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: 18px;
  gap: 8px;
}
.data-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #f8fafc;
  border-radius: 8px;
  box-shadow: 0 1px 4px #e3f2fd;
  padding: 10px 8px;
  flex: 1;
  min-width: 0;
}
.data-img {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  background: #f0f7fa;
  margin-bottom: 6px;
}
.data-text {
  font-size: 14px;
  color: #1565c0;
  font-weight: 600;
  text-align: center;
}
.show-purchase {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-top: 12px;
  background: #f8fafc;
  border-radius: 10px;
  box-shadow: 0 1px 4px #e3f2fd;
  padding: 12px 0;
  gap: 0;
  cursor: pointer; /* 鼠标悬浮变成手指 */
  transition: background 0.2s;
}
.show-purchase:hover {
  background: #e3f2fd; /* 悬浮时变色 */
}
.purchase-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}
.purchase-item div {
  font-size: 18px;
  font-weight: bold;
  color: #2193b0;
  margin-bottom: 4px;
}
.purchase-item span {
  font-size: 13px;
  color: #888;
}
@media (max-width: 900px) {
  .user-info-card {
    padding: 10px 4px 8px 4px;
    border-radius: 8px;
    box-shadow: none;
  }
  .user-info-header {
    gap: 8px;
  }
  .user_name {
    font-size: 16px;
  }
  .data-text {
    font-size: 12px;
  }
  .purchase-item div {
    font-size: 13px;
  }
}
</style>