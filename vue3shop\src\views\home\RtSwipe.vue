<template>
  <div class="swipe-container">
    <el-carousel 
      :interval="3000" 
      type="card" 
      height="380px"
      :autoplay="true"
      indicator-position="outside"
      arrow="always"
      :pause-on-hover="true"
    >
      <el-carousel-item v-for="item in images" :key="item.id">
        <div class="carousel-content">
          <img :src="item.src" :alt="'banner-' + item.id" />
        </div>
      </el-carousel-item>
    </el-carousel>
  </div>
</template>

<script setup>
import diy1 from '@/assets/images/swipe/1.jpg'
import diy2 from '@/assets/images/swipe/2.png'
import diy3 from '@/assets/images/swipe/3.png'

const images = [
  {id:1, src: diy1},
  {id:2, src: diy2},
  {id:3, src: diy3},
]
</script>

<style scoped>
.swipe-container {
  width: 100%;
  padding: 20px;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.carousel-content {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 12px;
  overflow: hidden;
  background: transparent;
}

.carousel-content img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: all 0.3s ease;
  padding: 10px;
}

/* 添加中间图片的样式 */
:deep(.el-carousel__item.is-active) {
  transform: scale(1.1);
  z-index: 2;
  background: transparent;
}

:deep(.el-carousel__item) {
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  background: transparent;
}

:deep(.el-carousel__arrow) {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  width: 44px;
  height: 44px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 3;
}

:deep(.el-carousel__arrow:hover) {
  background-color: #409EFF;
}

:deep(.el-carousel__indicators) {
  bottom: -20px;
}

:deep(.el-carousel__button) {
  width: 30px;
  height: 3px;
  border-radius: 3px;
  background-color: #409EFF;
}

:deep(.el-carousel__indicator.is-active .el-carousel__button) {
  background-color: #66b1ff;
}

/* 添加卡片轮播的样式 */
:deep(.el-carousel--card) {
  padding: 20px 0;
}

:deep(.el-carousel--card .el-carousel__item) {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

:deep(.el-carousel--card .el-carousel__item.is-active) {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}
</style>