package com.itheima.springbootcd.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import javax.validation.groups.Default;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 生产商/商家实体类
 */
@Data
@TableName("manufacturer")
public class Manufacturer implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "manufacturer_id", type = IdType.AUTO)
    @NotNull(groups = Update.class)
    private Integer manufacturerId;

    @TableField("manufacturer_name")
    @NotEmpty
    private String manufacturerName;//生产商名称

    @TableField("description")
    private String description;//生产商描述

    @TableField("logo_url")
    private String logoUrl;//生产商logo

    @TableField("contact_info")
    private String contactInfo;//联系方式

    @TableField("address")
    private String address;//地址

    @TableField("website")
    private String website;//官网

    @TableField("status")
    private Integer status;//状态(0-正常,1-禁用)

    @TableField("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @TableField("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @TableField("create_user")
    private Integer createUser;//创建者用户ID

    // 分组校验
    public interface Add extends Default {
    }

    public interface Update extends Default {
    }
}
