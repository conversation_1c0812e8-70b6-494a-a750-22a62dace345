<template>
  <div class="category-detail-page">
    <!-- 返回上一页按钮 -->
    <div class="back-button">
      <el-tooltip content="返回上一页" placement="right" effect="dark">
        <el-button type="primary" :icon="ArrowLeft" circle @click="goBack" />
      </el-tooltip>
    </div>

    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">{{ categoryInfo.categoryName || '分类详情' }}</h1>
    </div>

    <div class="category-detail-container">
      <!-- 分类信息卡片 -->
      <el-card class="category-info-card">
        <template #header>
          <div class="header">
            <span class="title">分类信息</span>
          </div>
        </template>
        <div class="category-info">
          <div class="info-item">
            <span class="label">分类名称：</span>
            <span class="value">{{ categoryInfo.categoryName }}</span>
          </div>
          <div class="info-item">
            <span class="label">分类描述：</span>
            <span class="value">{{ categoryInfo.description }}</span>
          </div>
          <div class="info-item">
            <span class="label">创建时间：</span>
            <span class="value">{{ categoryInfo.createTime }}</span>
          </div>
        </div>
      </el-card>

      <!-- 该分类下的商品列表 -->
      <el-card class="products-card">
        <template #header>
          <div class="header">
            <span class="title">该分类下的商品</span>
            <span class="count">({{ products.length }}件商品)</span>
          </div>
        </template>
        
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="3" animated />
        </div>
        
        <div v-else-if="products.length === 0" class="empty-container">
          <el-empty description="该分类下暂无商品" />
        </div>
        
        <div v-else class="products-grid">
          <div 
            v-for="product in products" 
            :key="product.productId"
            class="product-card"
            @click="goToProductDetail(product.productId)"
          >
            <div class="product-image">
              <img :src="product.imagePath || defaultImage" :alt="product.productName" />
            </div>
            <div class="product-info">
              <h3 class="product-name">{{ product.productName }}</h3>
              <p class="product-description">{{ product.description }}</p>
              <div class="product-footer">
                <span class="product-price">¥{{ product.price }}</span>
                <span class="product-stock">库存: {{ product.stockQuantity }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>
    <RtTabBar />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'

import RtTabBar from '@/components/RtTabBar.vue'
import { productCategoryDetailService, productListByCategoryService } from '@/api/product'
import { useUserStore } from '@/stores/user'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

const categoryInfo = ref({})
const products = ref([])
const loading = ref(false)
const defaultImage = '/src/assets/images/default-product.png'

// 获取分类详情
const getCategoryDetail = async (categoryId) => {
  try {
    const result = await productCategoryDetailService(categoryId)
    if (result && result.data) {
      categoryInfo.value = result.data
    } else {
      ElMessage.error('获取分类详情失败')
    }
  } catch (error) {
    console.error('获取分类详情失败:', error)
    ElMessage.error('获取分类详情失败')
  }
}

// 获取该分类下的商品列表
const getProductsByCategory = async (categoryId) => {
  try {
    loading.value = true
    const result = await productListByCategoryService(categoryId)
    if (result && result.data) {
      products.value = result.data
    } else {
      ElMessage.error('获取商品列表失败')
    }
  } catch (error) {
    console.error('获取商品列表失败:', error)
    ElMessage.error('获取商品列表失败')
  } finally {
    loading.value = false
  }
}

// 跳转到商品详情页
const goToProductDetail = (productId) => {
  router.push({ name: 'ProductDetail', params: { id: productId } })
}

// 返回上一页
const goBack = () => {
  router.back()
}

onMounted(async () => {
  const categoryId = route.params.id
  if (!categoryId) {
    ElMessage.error('分类ID不存在')
    router.back()
    return
  }

  if (!userStore.checkLogin()) {
    return
  }

  await getCategoryDetail(categoryId)
  await getProductsByCategory(categoryId)
})
</script>

<style lang="scss" scoped>
.category-detail-page {
  min-height: 100vh;
  background: #f6f8fa;
  position: relative;
}

.back-button {
  position: fixed;
  bottom: 100px;
  right: 20px;
  z-index: 1000;

  .el-button {
    background: linear-gradient(135deg, #2193b0 0%, #1565c0 100%);
    border: none;
    box-shadow: 0 4px 12px rgba(33, 147, 176, 0.3);
    transition: all 0.3s ease;

    &:hover {
      background: linear-gradient(135deg, #1565c0 0%, #0d47a1 100%);
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(33, 147, 176, 0.4);
    }
  }
}

.page-header {
  background: linear-gradient(135deg, #2193b0 0%, #1565c0 100%);
  color: white;
  padding: 20px;
  text-align: center;
  margin-top: 20px;
  border-radius: 0 0 20px 20px;
  box-shadow: 0 4px 12px rgba(33, 147, 176, 0.2);

  .page-title {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    letter-spacing: 1px;
  }
}

.category-detail-container {
  padding: 20px;
  margin-top: 20px;
}

.category-info-card,
.products-card {
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 2px 12px #e3f2fd;
  margin-bottom: 20px;
  
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .title {
      font-size: 18px;
      font-weight: bold;
      color: #1565c0;
    }
    
    .count {
      font-size: 14px;
      color: #666;
    }
  }
}

.category-info {
  .info-item {
    display: flex;
    margin-bottom: 16px;
    
    .label {
      font-weight: 500;
      color: #333;
      width: 100px;
    }
    
    .value {
      color: #666;
      flex: 1;
    }
  }
}

.loading-container,
.empty-container {
  padding: 40px 0;
  text-align: center;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.product-card {
  border: 1px solid #e3f2fd;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(33, 147, 176, 0.15);
  }
  
  .product-image {
    height: 200px;
    overflow: hidden;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  
  .product-info {
    padding: 16px;
    
    .product-name {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      margin: 0 0 8px 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    
    .product-description {
      font-size: 14px;
      color: #666;
      margin: 0 0 12px 0;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
    }
    
    .product-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .product-price {
        font-size: 18px;
        font-weight: bold;
        color: #e74c3c;
      }
      
      .product-stock {
        font-size: 12px;
        color: #999;
      }
    }
  }
}

:deep(.el-card) {
  --el-card-border-color: #e3f2fd;
}
</style>
