import request from '@/utils/request'
import { useUserStore } from '@/stores/user'

// 添加收藏
export const addCollectService = (productId) => {
  const userStore = useUserStore()
  return request.post('/collect', { productId }, {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': userStore.token
    }
  })
}

// 取消收藏
export const deleteCollectService = (collectId) => {
  const userStore = useUserStore()
  return request.delete(`/collect/${collectId}`, {}, {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': userStore.token
    }
  })
}

// 获取收藏列表
export const getCollectListService = (page = 1, pageSize = 10) => {
  const userStore = useUserStore()
  return request.get('/collect/list', {
    params: { page, pageSize },
    headers: {
      'Authorization': userStore.token
    }
  })
}