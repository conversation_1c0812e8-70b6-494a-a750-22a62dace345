package com.itheima.springbootcd.controller;

import com.itheima.springbootcd.annotation.RequireRole;
import com.itheima.springbootcd.enums.UserRole;
import com.itheima.springbootcd.pojo.Manufacturer;
import com.itheima.springbootcd.pojo.Result;
import com.itheima.springbootcd.service.ManufacturerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 生产商控制器
 */
@RestController
@RequestMapping("/manufacturer")
@Validated
public class ManufacturerController {
    
    @Autowired
    private ManufacturerService manufacturerService;
    
    /**
     * 获取生产商列表（公开访问）
     */
    @GetMapping("/list")
    public Result<List<Manufacturer>> list() {
        List<Manufacturer> manufacturers = manufacturerService.list();
        return Result.success(manufacturers);
    }
    
    /**
     * 获取生产商详情（公开访问）
     */
    @GetMapping("/{id}/detail")
    public Result<Manufacturer> detail(@PathVariable Integer id) {
        Manufacturer manufacturer = manufacturerService.findById(id);
        if (manufacturer == null) {
            return Result.error("生产商不存在");
        }
        return Result.success(manufacturer);
    }
    
    /**
     * 添加生产商（仅管理员可操作）
     */
    @PostMapping("/add")
    @RequireRole(UserRole.ADMIN)
    public Result add(@RequestBody @Validated(Manufacturer.Add.class) Manufacturer manufacturer) {
        manufacturerService.add(manufacturer);
        return Result.success();
    }
    
    /**
     * 更新生产商信息（管理员或对应的生产商用户可操作）
     */
    @PutMapping("/update")
    @RequireRole({UserRole.ADMIN, UserRole.MANUFACTURER})
    public Result update(@RequestBody @Validated(Manufacturer.Update.class) Manufacturer manufacturer) {
        manufacturerService.update(manufacturer);
        return Result.success();
    }
    
    /**
     * 删除生产商（仅管理员可操作）
     */
    @DeleteMapping("/{id}")
    @RequireRole(UserRole.ADMIN)
    public Result delete(@PathVariable Integer id) {
        manufacturerService.deleteById(id);
        return Result.success();
    }
    
    /**
     * 获取当前用户的生产商信息（生产商用户专用）
     */
    @GetMapping("/my")
    @RequireRole(UserRole.MANUFACTURER)
    public Result<Manufacturer> getMyManufacturer() {
        // 这里需要从ThreadLocal获取当前用户ID
        // 然后查询对应的生产商信息
        return Result.success();
    }
}
