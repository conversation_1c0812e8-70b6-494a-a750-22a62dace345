package com.itheima.springbootcd.controller;

import com.itheima.springbootcd.pojo.Cart;
import com.itheima.springbootcd.pojo.Result;
import com.itheima.springbootcd.service.ICartService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@RestController
@RequestMapping("/cart")
public class CartController {
    
    @Autowired
    private ICartService cartService;

    @PostMapping("/add")
    public Result addToCart(@RequestBody Cart cart) {
        boolean success = cartService.addToCart(cart);
        return success ? Result.success() : Result.error("添加购物车失败");
    }

    @PutMapping("/update")
    public Result updateQuantity(@RequestBody Cart cart) {
        boolean success = cartService.updateQuantity(cart.getCartId(), cart.getQuantity());
        return success ? Result.success() : Result.error("更新数量失败");
    }

    @DeleteMapping("/remove")
    public Result removeCartItem(@RequestParam Integer cartId) {
        boolean success = cartService.removeCartItem(cartId);
        return success ? Result.success() : Result.error("删除商品失败");
    }

    @GetMapping("/list")
    public Result getCartList(@RequestParam Long userId) {
        List<Cart> cartList = cartService.getCartList(userId);
        return Result.success(cartList);
    }

    @DeleteMapping("/clear")
    public Result clearCart(@RequestParam Long userId) {
        boolean success = cartService.clearCart(userId);
        return success ? Result.success() : Result.error("清空购物车失败");
    }
}
