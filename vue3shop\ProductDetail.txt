<template>
  <div class="product-detail">
    <!-- 返回主页按钮 -->
    <div class="back-home">
      <el-tooltip content="返回首页" placement="left" effect="dark">
        <el-button type="primary" :icon="House" circle @click="router.push('/')"/>
      </el-tooltip>
    </div>

    <!-- 商品图片区域 -->
    <div class="image-container">
      <el-carousel :interval="4000" arrow="always" height="40vh">
        <el-carousel-item v-for="(image, index) in product.images" :key="index">
          <img :src="image" alt="Product Image" class="carousel-image"/>
        </el-carousel-item>
      </el-carousel>
    </div>

    <!-- 商品信息区域 -->
    <div class="product-info">
      <!-- 商品名称 -->
      <div class="product-name">{{ product.name }}</div>
      <!-- 商品价格 -->
      <div class="product-price">{{ product.price }}</div>
      <!-- 商品库存 -->
      <div
        class="product-stockQuantity"
        :class="{
          'stockQuantity-red': product.stockQuantity >= 1 && product.stockQuantity <= 5,
          'stockQuantity-grey': product.stockQuantity === 0,
        }"
      >
        {{ product.stockQuantity }}
        <div class="quantity-input">
          <el-input-number v-model="quantity" @change="handleChange" :min="1" :max="product.stockQuantity" label="描述文字"></el-input-number>
        </div>
      </div>
      <!-- 商品描述 -->
      <div class="product-desc">{{ product.description }}</div>
      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button type="primary" round>加入购物车</el-button>
        <el-button type="warning" round>收藏</el-button>
        <el-button type="danger" round :disabled="product.stockQuantity === 0">
          {{ product.stockQuantity === 0 ? "售罄" : "购买" }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useProductStore } from "@/stores/product";
import { House } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus"; // 引入 ElMessage

const route = useRoute();
const router = useRouter();
const productId = route.params.id;
const productStore = useProductStore();

// 定义商品详情数据
const product = ref({
  name: "商品名称",
  price: 99.99,
  stockQuantity: 1,
  description: "这是一个商品的详细描述，展示商品的特点和功能。",
  images: [
    "https://via.placeholder.com/300x200?text=Image1",
    "https://via.placeholder.com/300x200?text=Image2",
    "https://via.placeholder.com/300x200?text=Image3",
  ],
});

// 获取商品详情
const getProductDetail = async () => {
  const productDetail = await productStore.getProductDetail(productId);
  if (productDetail) {
    product.value = productDetail;
  }
};

onMounted(() => {
  getProductDetail();
});

// 购买数量状态
const quantity = ref(1);

// 弹窗提示方法
const showErrorMessage = (message) => {
  ElMessage.error({ message, duration: 2000 }); // 弹出错误提示，持续 2 秒
};

// 减少数量
const decrementQuantity = () => {
  if (quantity.value > 1) {
    quantity.value -= 1;
  } else {
    showErrorMessage("已达最小值，无法再减少");
  }
};

// 增加数量
const incrementQuantity = () => {
  if (quantity.value < product.value.stockQuantity) {
    quantity.value += 1;
  } else {
    showErrorMessage("已达最大值，无法再增加");
  }
};

// 处理数量变化
const handleChange = (value) => {
  if (value > product.value.stockQuantity) {
    showErrorMessage("库存不足");
    quantity.value = product.value.stockQuantity;
  } else if (value < 1) {
    showErrorMessage("数量不能少于1");
    quantity.value = 1;
  }
};
</script>

<style scoped>
.product-detail {
  display: flex;
  min-height: 80vh;
  width: 90vw;
  max-width: 1200px;
  margin: 2rem auto;
  background-color: #ffffff;
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
}

/* 商品图片区域 */
.image-container {
  width: 55%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f8f9fa;
  padding: 2rem;
}

.el-carousel {
  width: 100%;
  height: 100%;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.el-carousel__item {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #ffffff;
}

.carousel-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: transform 0.3s ease;
}

.carousel-image:hover {
  transform: scale(1.02);
}

/* 商品信息区域 */
.product-info {
  width: 45%;
  padding: 3rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  background: linear-gradient(to bottom, #ffffff, #f8f9fa);
}

/* 商品名称 */
.product-name {
  font-size: 2rem;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 1.4;
  margin-bottom: 0.5rem;
}

/* 商品价格 */
.product-price {
  font-size: 2.5rem;
  font-weight: 700;
  color: #ff4d4f;
  margin: 1rem 0;
  display: flex;
  align-items: baseline;
}

.product-price::before {
  content: "¥";
  font-size: 1.5rem;
  margin-right: 0.25rem;
}

/* 商品库存 */
.product-stockQuantity {
  font-size: 2rem;
  font-weight: 450;
  color: #333;
  line-height: 1.6;
  margin: 0.5rem 0;
  display: flex;
  justify-content: space-between; /* 使库存信息和计数器分居两端 */
}

.product-stockQuantity::before {
  content: "库存:";
  font-size: 1.5rem;
  margin-right: 0.25rem;
}

/* 购买数量输入样式 */
.quantity-input {
  display: flex;
  justify-content: flex-end; /* 使子元素靠右对齐 */
}

/* 红色样式 */
.stockQuantity-red {
  color: red;
  font-weight: bold;
}

/* 淡灰色样式 */
.stockQuantity-grey {
  color: #ccc;
  font-weight: 400;
}

/* 商品描述 */
.product-desc {
  font-size: 1rem;
  color: #666;
  line-height: 1.6;
  margin: 1rem 0;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #1890ff;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
  padding: 1rem 0;
}

/* 按钮样式 */
.el-button {
  flex: 1;
  height: 48px;
  font-size: 1rem;
  font-weight: 500;
  border-radius: 24px;
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.el-button:active {
  transform: translateY(0);
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .product-detail {
    flex-direction: column;
    width: 95vw;
    margin: 1rem auto;
  }

  .image-container,
  .product-info {
    width: 100%;
  }

  .image-container {
    height: 50vh;
    padding: 1rem;
  }

  .product-info {
    padding: 1.5rem;
  }

  .product-name {
    font-size: 1.5rem;
  }

  .product-price {
    font-size: 2rem;
  }

  .action-buttons {
    flex-direction: column;
  }

  .el-button {
    width: 100%;
  }
}

/* 返回主页按钮样式 */
.back-home {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10;
}

.back-home .el-button {
  width: 40px;
  height: 40px;
  padding: 8px;
  background-color: #409eff;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  color: #fff;
}

.back-home .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.25);
  background-color: #66b1ff;
}

.back-home .el-button:active {
  transform: translateY(0);
  background-color: #3a8ee6;
}
</style>
