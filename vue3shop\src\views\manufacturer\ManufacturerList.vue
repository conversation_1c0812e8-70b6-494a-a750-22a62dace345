<template>
  <div class="manufacturer-list">
    <RtHeader title="生产商列表" />
    
    <div class="container">
      <div class="manufacturer-grid">
        <div 
          v-for="manufacturer in manufacturers" 
          :key="manufacturer.manufacturerId"
          class="manufacturer-card"
          @click="goToDetail(manufacturer.manufacturerId)"
        >
          <div class="manufacturer-logo">
            <img 
              :src="manufacturer.logoUrl || '/src/assets/images/default-manufacturer.png'" 
              :alt="manufacturer.manufacturerName"
              @error="handleImageError"
            />
          </div>
          
          <div class="manufacturer-info">
            <h3 class="manufacturer-name">{{ manufacturer.manufacturerName }}</h3>
            <p class="manufacturer-desc">{{ manufacturer.description || '暂无描述' }}</p>
            
            <div class="manufacturer-meta">
              <span v-if="manufacturer.website" class="website">
                <el-icon><Link /></el-icon>
                官网
              </span>
              <span v-if="manufacturer.address" class="address">
                <el-icon><Location /></el-icon>
                {{ manufacturer.address }}
              </span>
            </div>
          </div>
          
          <div class="manufacturer-actions">
            <el-button type="primary" size="small">查看详情</el-button>
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-if="manufacturers.length === 0" class="empty-state">
        <el-empty description="暂无生产商信息" />
      </div>
    </div>
    
    <RtTabBar />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Link, Location } from '@element-plus/icons-vue'
import { manufacturerListService } from '@/api/manufacturer'

const router = useRouter()
const manufacturers = ref([])
const loading = ref(false)

// 获取生产商列表
const getManufacturerList = async () => {
  try {
    loading.value = true
    const result = await manufacturerListService()
    
    if (result && result.code === 0) {
      manufacturers.value = result.data || []
    } else {
      ElMessage.error(result?.message || '获取生产商列表失败')
    }
  } catch (error) {
    console.error('获取生产商列表失败:', error)
    ElMessage.error('获取生产商列表失败')
  } finally {
    loading.value = false
  }
}

// 跳转到生产商详情
const goToDetail = (manufacturerId) => {
  router.push(`/manufacturer/${manufacturerId}/detail`)
}

// 处理图片加载错误
const handleImageError = (event) => {
  event.target.src = '/src/assets/images/default-manufacturer.png'
}

onMounted(() => {
  getManufacturerList()
})
</script>

<style scoped>
.manufacturer-list {
  min-height: 100vh;
  background: #f6f8fa;
}

.container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.manufacturer-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 80px;
}

.manufacturer-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid #e4e7ed;
}

.manufacturer-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  border-color: #409EFF;
}

.manufacturer-logo {
  text-align: center;
  margin-bottom: 16px;
}

.manufacturer-logo img {
  width: 80px;
  height: 80px;
  object-fit: contain;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  padding: 8px;
}

.manufacturer-info {
  margin-bottom: 16px;
}

.manufacturer-name {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
  text-align: center;
}

.manufacturer-desc {
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
  margin: 0 0 12px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.manufacturer-meta {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.manufacturer-meta span {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #909399;
  gap: 4px;
}

.manufacturer-actions {
  text-align: center;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
}

@media (max-width: 768px) {
  .container {
    padding: 16px;
  }
  
  .manufacturer-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .manufacturer-card {
    padding: 16px;
  }
}
</style>
