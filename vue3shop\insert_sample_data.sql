-- 插入示例数据
-- 注意：密码都是123456的MD5值：e10adc3949ba59abbe56e057f20f883e

-- ----------------------------
-- 1. 插入用户数据
-- ----------------------------

-- 管理员用户
INSERT INTO `user` (`username`, `password`, `email`, `phone`, `register_time`, `status`, `role`, `nickname`, `address`) VALUES
('admin', 'e10adc3949ba59abbe56e057f20f883e', '<EMAIL>', '13800000000', NOW(), 0, 'ADMIN', '系统管理员', '管理员地址');

-- 普通用户
INSERT INTO `user` (`username`, `password`, `email`, `phone`, `register_time`, `status`, `role`, `nickname`, `address`) VALUES
('user001', 'e10adc3949ba59abbe56e057f20f883e', '<EMAIL>', '13800000001', NOW(), 0, 'CUSTOMER', '张三', '北京市朝阳区'),
('user002', 'e10adc3949ba59abbe56e057f20f883e', '<EMAIL>', '13800000002', NOW(), 0, 'CUSTOMER', '李四', '上海市浦东新区'),
('user003', 'e10adc3949ba59abbe56e057f20f883e', '<EMAIL>', '13800000003', NOW(), 0, 'CUSTOMER', '王五', '广州市天河区');

-- ----------------------------
-- 2. 插入生产商数据
-- ----------------------------
INSERT INTO `manufacturer` (`manufacturer_name`, `description`, `logo_url`, `contact_info`, `address`, `website`, `status`, `create_user`) VALUES
('华硕科技', '华硕是全球领先的3C解决方案提供商，致力于为个人和企业用户提供最具创新价值的产品及应用方案。产品线完整覆盖笔记本电脑、主板、显卡、服务器、光存储、有线/无线网络通讯产品、LCD、掌上电脑、智能手机等全线3C产品。', 'https://example.com/logos/asus.png', '400-600-6655', '台湾台北市北投区立业街2号', 'https://www.asus.com.cn', 0, 1),

('微星科技', 'MSI微星科技为全球前三大主机板制造商，并跨足到显示卡、笔记本电脑、服务器、工业电脑、多媒体、通讯产品及车用电子等领域。以"Innovation with Style"为品牌精神，致力于满足消费者在数字家庭娱乐方面的需求。', 'https://example.com/logos/msi.png', '400-820-3365', '台湾新北市中和区立德街69号', 'https://cn.msi.com', 0, 1),

('技嘉科技', '技嘉科技是台湾的电脑硬件生产商，以主板和显卡为主要产品。技嘉致力于成为全球主板、显卡及硬件解决方案的领导品牌。', 'https://example.com/logos/gigabyte.png', '************', '台湾台北市内湖区瑞光路581号', 'https://www.gigabyte.cn', 0, 1),

('英特尔', '英特尔公司是全球最大的半导体芯片制造商，它成立于1968年，具有52年产品创新和市场领导的历史。1971年，英特尔推出了全球第一个微处理器。', 'https://example.com/logos/intel.png', '************', '美国加利福尼亚州圣克拉拉', 'https://www.intel.cn', 0, 1),

('AMD', 'AMD是一家专注于微处理器设计和制造的公司，为计算机、通信和消费电子行业设计和制造各种创新的微处理器、闪存和低功率处理器解决方案。', 'https://example.com/logos/amd.png', '************', '美国加利福尼亚州桑尼维尔', 'https://www.amd.com/zh-hans', 0, 1);

-- 生产商用户（关联到生产商）
INSERT INTO `user` (`username`, `password`, `email`, `phone`, `register_time`, `status`, `role`, `nickname`, `address`, `manufacturer_id`) VALUES
('asus_manager', 'e10adc3949ba59abbe56e057f20f883e', '<EMAIL>', '13800001001', NOW(), 0, 'MANUFACTURER', '华硕管理员', '华硕公司地址', 1),
('msi_manager', 'e10adc3949ba59abbe56e057f20f883e', '<EMAIL>', '13800001002', NOW(), 0, 'MANUFACTURER', '微星管理员', '微星公司地址', 2),
('gigabyte_manager', 'e10adc3949ba59abbe56e057f20f883e', '<EMAIL>', '13800001003', NOW(), 0, 'MANUFACTURER', '技嘉管理员', '技嘉公司地址', 3),
('intel_manager', 'e10adc3949ba59abbe56e057f20f883e', '<EMAIL>', '13800001004', NOW(), 0, 'MANUFACTURER', '英特尔管理员', '英特尔公司地址', 4),
('amd_manager', 'e10adc3949ba59abbe56e057f20f883e', '<EMAIL>', '13800001005', NOW(), 0, 'MANUFACTURER', 'AMD管理员', 'AMD公司地址', 5);

-- ----------------------------
-- 3. 插入商品分类数据
-- ----------------------------
INSERT INTO `category` (`category_name`, `description`, `create_user`) VALUES
('CPU', 'Central Processing Unit 中央处理器', 1),
('主板', 'Motherboard 主板', 1),
('内存', 'Memory 内存条', 1),
('显卡', 'Graphics Card 显卡', 1),
('硬盘', 'Hard Disk Drive 硬盘存储', 1),
('电源', 'Power Supply Unit 电源', 1),
('机箱', 'Computer Case 机箱', 1),
('键盘', 'Keyboard 键盘', 1),
('鼠标', 'Mouse 鼠标', 1),
('显示器', 'Monitor 显示器', 1),
('散热器', 'Cooler 散热器', 1),
('风扇', 'Fan 风扇', 1),
('耳机', 'Headphone 耳机', 1),
('DIY电脑', 'DIY Computer 组装电脑', 1);

-- ----------------------------
-- 4. 插入商品数据
-- ----------------------------

-- CPU商品
INSERT INTO `product` (`product_name`, `category_id`, `manufacturer_id`, `price`, `stock_quantity`, `description`, `image_path`) VALUES
('Intel Core i9-13900K', 1, 4, 4299.00, 50, 'Intel第13代酷睿处理器，24核32线程，基础频率3.0GHz，最大睿频5.8GHz', 'https://example.com/products/i9-13900k.jpg'),
('Intel Core i7-13700K', 1, 4, 3199.00, 80, 'Intel第13代酷睿处理器，16核24线程，基础频率3.4GHz，最大睿频5.4GHz', 'https://example.com/products/i7-13700k.jpg'),
('AMD Ryzen 9 7900X', 1, 5, 3899.00, 60, 'AMD锐龙7000系列处理器，12核24线程，基础频率4.7GHz，最大频率5.6GHz', 'https://example.com/products/ryzen-9-7900x.jpg'),
('AMD Ryzen 7 7700X', 1, 5, 2699.00, 90, 'AMD锐龙7000系列处理器，8核16线程，基础频率4.5GHz，最大频率5.4GHz', 'https://example.com/products/ryzen-7-7700x.jpg');

-- 主板商品
INSERT INTO `product` (`product_name`, `category_id`, `manufacturer_id`, `price`, `stock_quantity`, `description`, `image_path`) VALUES
('华硕ROG STRIX Z790-E GAMING WIFI', 2, 1, 2899.00, 30, '华硕ROG玩家国度Z790主板，支持Intel第13代处理器，WiFi 6E，雷电4', 'https://example.com/products/asus-z790-e.jpg'),
('微星MAG Z790 TOMAHAWK WIFI', 2, 2, 1999.00, 45, '微星Z790战斧主板，支持Intel第13代处理器，WiFi 6E，DDR5内存', 'https://example.com/products/msi-z790-tomahawk.jpg'),
('技嘉B650 AORUS ELITE AX', 2, 3, 1599.00, 55, '技嘉B650主板，支持AMD锐龙7000系列，WiFi 6E，PCIe 5.0', 'https://example.com/products/gigabyte-b650-elite.jpg');

-- 显卡商品
INSERT INTO `product` (`product_name`, `category_id`, `manufacturer_id`, `price`, `stock_quantity`, `description`, `image_path`) VALUES
('华硕ROG STRIX RTX 4090 24GB', 4, 1, 12999.00, 15, '华硕ROG RTX 4090显卡，24GB GDDR6X显存，支持光线追踪和DLSS 3', 'https://example.com/products/asus-rtx4090.jpg'),
('微星RTX 4080 GAMING X TRIO 16GB', 4, 2, 8999.00, 25, '微星RTX 4080显卡，16GB GDDR6X显存，三风扇散热设计', 'https://example.com/products/msi-rtx4080.jpg'),
('技嘉RTX 4070 Ti GAMING OC 12GB', 4, 3, 6299.00, 40, '技嘉RTX 4070 Ti显卡，12GB GDDR6X显存，超频版本', 'https://example.com/products/gigabyte-rtx4070ti.jpg');

-- 内存商品
INSERT INTO `product` (`product_name`, `category_id`, `manufacturer_id`, `price`, `stock_quantity`, `description`, `image_path`) VALUES
('华硕ROG STRIX DDR5-5600 32GB套装', 3, 1, 1299.00, 70, '华硕ROG DDR5内存，5600MHz频率，32GB(16GBx2)套装，RGB灯效', 'https://example.com/products/asus-ddr5-32gb.jpg'),
('微星DDR5-5200 16GB套装', 3, 2, 699.00, 100, '微星DDR5内存，5200MHz频率，16GB(8GBx2)套装', 'https://example.com/products/msi-ddr5-16gb.jpg');

-- DIY电脑整机
INSERT INTO `product` (`product_name`, `category_id`, `manufacturer_id`, `price`, `stock_quantity`, `description`, `image_path`) VALUES
('华硕ROG游戏主机-高端配置', 14, 1, 25999.00, 10, 'i9-13900K + RTX 4090 + 32GB DDR5 + 2TB SSD，顶级游戏配置', 'https://example.com/products/asus-gaming-pc-high.jpg'),
('微星电竞主机-中端配置', 14, 2, 15999.00, 15, 'i7-13700K + RTX 4070 Ti + 16GB DDR5 + 1TB SSD，性价比电竞配置', 'https://example.com/products/msi-gaming-pc-mid.jpg'),
('技嘉创作者主机', 14, 3, 18999.00, 12, 'Ryzen 9 7900X + RTX 4080 + 32GB DDR5 + 2TB SSD，专业创作配置', 'https://example.com/products/gigabyte-creator-pc.jpg');

-- ----------------------------
-- 5. 插入一些示例购物车数据
-- ----------------------------
INSERT INTO `cart` (`user_id`, `product_id`, `quantity`) VALUES
(2, 1, 1),  -- user001的购物车：Intel i9-13900K
(2, 5, 1),  -- user001的购物车：华硕Z790主板
(3, 3, 1),  -- user002的购物车：AMD Ryzen 9 7900X
(3, 8, 1);  -- user002的购物车：华硕RTX 4090

-- ----------------------------
-- 6. 插入一些示例收藏数据
-- ----------------------------
INSERT INTO `collect` (`user_id`, `product_id`) VALUES
(2, 1),   -- user001收藏Intel i9-13900K
(2, 8),   -- user001收藏华硕RTX 4090
(3, 3),   -- user002收藏AMD Ryzen 9 7900X
(3, 14),  -- user002收藏华硕游戏主机
(4, 6),   -- user003收藏微星Z790主板
(4, 9);   -- user003收藏微星RTX 4080

-- ----------------------------
-- 7. 插入一些示例购买记录
-- ----------------------------
INSERT INTO `user_purchase` (`user_id`, `product_id`, `quantity`, `purchase_price`, `total_amount`, `status`) VALUES
(2, 2, 1, 3199.00, 3199.00, 3),  -- user001购买了Intel i7-13700K，已完成
(3, 4, 1, 2699.00, 2699.00, 2),  -- user002购买了AMD Ryzen 7 7700X，已发货
(4, 11, 2, 699.00, 1398.00, 1);  -- user003购买了2套微星DDR5内存，已支付

-- 查询验证数据
SELECT '=== 用户统计 ===' as info;
SELECT role, COUNT(*) as count FROM user GROUP BY role;

SELECT '=== 生产商列表 ===' as info;
SELECT manufacturer_id, manufacturer_name, status FROM manufacturer;

SELECT '=== 商品分类统计 ===' as info;
SELECT c.category_name, COUNT(p.product_id) as product_count
FROM category c
LEFT JOIN product p ON c.category_id = p.category_id
GROUP BY c.category_id, c.category_name;

SELECT '=== 生产商商品统计 ===' as info;
SELECT 
    m.manufacturer_name,
    COUNT(p.product_id) as product_count,
    AVG(p.price) as avg_price
FROM manufacturer m
LEFT JOIN product p ON m.manufacturer_id = p.manufacturer_id
GROUP BY m.manufacturer_id, m.manufacturer_name;
