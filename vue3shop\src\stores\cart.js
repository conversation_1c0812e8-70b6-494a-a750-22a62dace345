import { defineStore } from 'pinia'
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import request from '@/utils/request'
import { useUserStore } from '@/stores/user'

export const useCartStore = defineStore('cart', () => {
  const cartList = ref([])
  const loading = ref(false)
  const userStore = useUserStore()

  // 获取购物车列表
  const getCartList = async () => {
    if (!userStore.token) {
      console.log('未登录，无法获取购物车列表')
      return false
    }

    try {
      loading.value = true
      console.log('开始获取购物车列表')
      
      // 确保先获取用户信息
      if (!userStore.userInfo) {
        console.log('用户信息不存在，尝试获取用户信息')
        const userInfoResult = await userStore.getUserInfo()
        if (!userInfoResult) {
          console.error('获取用户信息失败，无法获取购物车列表')
          return false
        }
      }
      
      // 检查用户ID
      const userId = userStore.userInfo?.userId || userStore.userInfo?.id
      if (!userId) {
        console.error('用户ID不存在，无法获取购物车列表')
        ElMessage.error('获取用户信息失败')
        return false
      }

      console.log('开始获取购物车列表，当前用户ID:', userId)
      const result = await request.get('/cart/list', {
        params: {
          userId: userId
        }
      })
      console.log('获取购物车列表结果:', result)
      
      if (result && result.data) {
        cartList.value = result.data
        return true
      } else {
        console.error('获取购物车列表失败: 返回数据格式不正确', result)
        return false
      }
    } catch (error) {
      console.error('获取购物车列表失败:', error)
      if (error.response?.status === 401) {
        ElMessage.error('登录已过期，请重新登录')
        userStore.logout()
        return false
      }
      ElMessage.error('获取购物车列表失败')
      return false
    } finally {
      loading.value = false
    }
  }

  // 添加商品到购物车
  const addToCart = async (productId, quantity = 1) => {
    try {
      if (!userStore.userInfo?.id) {
        await userStore.getUserInfo()
        if (!userStore.userInfo?.id) {
          ElMessage.error('获取用户信息失败')
          return false
        }
      }

      const response = await request.post('/cart/add', {
        userId: userStore.userInfo.id,
        productId,
        quantity
      })
      if (response.code === 0) {
        ElMessage.success('添加成功')
        await getCartList()
        return true
      } else {
        ElMessage.error(response.message || '添加失败')
        return false
      }
    } catch (error) {
      if (error.response?.status === 401) {
        ElMessage.error('登录已过期，请重新登录')
        userStore.logout()
        return false
      }
      ElMessage.error('添加失败')
      console.error('添加失败:', error)
      return false
    }
  }

  // 更新购物车商品数量
  const updateCartItem = async (cartId, quantity) => {
    try {
      const response = await request.put('/cart/update', {
        cartId,
        quantity
      })
      if (response.code === 0) {
        ElMessage.success('更新成功')
        await getCartList()
        return true
      } else {
        ElMessage.error(response.message || '更新失败')
        return false
      }
    } catch (error) {
      if (error.response?.status === 401) {
        ElMessage.error('登录已过期，请重新登录')
        userStore.logout()
        return false
      }
      ElMessage.error('更新失败')
      console.error('更新失败:', error)
      return false
    }
  }

  // 删除购物车商品
  const removeFromCart = async (cartId) => {
    try {
      const response = await request.delete('/cart/remove', {
        params: { cartId }
      })
      if (response.code === 0) {
        ElMessage.success('删除成功')
        await getCartList()
        return true
      } else {
        ElMessage.error(response.message || '删除失败')
        return false
      }
    } catch (error) {
      if (error.response?.status === 401) {
        ElMessage.error('登录已过期，请重新登录')
        userStore.logout()
        return false
      }
      ElMessage.error('删除失败')
      console.error('删除失败:', error)
      return false
    }
  }

  // 清空购物车
  const clearCart = async () => {
    try {
      if (!userStore.userInfo?.id) {
        await userStore.getUserInfo()
        if (!userStore.userInfo?.id) {
          ElMessage.error('获取用户信息失败')
          return false
        }
      }

      const response = await request.delete('/cart/clear', {
        params: { userId: userStore.userInfo.id }
      })
      if (response.code === 0) {
        ElMessage.success('清空成功')
        cartList.value = []
        return true
      } else {
        ElMessage.error(response.message || '清空失败')
        return false
      }
    } catch (error) {
      if (error.response?.status === 401) {
        ElMessage.error('登录已过期，请重新登录')
        userStore.logout()
        return false
      }
      ElMessage.error('清空失败')
      console.error('清空失败:', error)
      return false
    }
  }

  return {
    cartList,
    loading,
    getCartList,
    addToCart,
    updateCartItem,
    removeFromCart,
    clearCart
  }
}, {
  persist: {
    key: 'cart-store',
    storage: localStorage,
    paths: ['cartList']  // 只持久化购物车列表
  }
}) 