package com.itheima.springbootcd.config;

import com.itheima.springbootcd.interceptors.LoginInterceptor;
import com.itheima.springbootcd.interceptors.RoleInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebConfig implements WebMvcConfigurer {
    @Autowired
    private LoginInterceptor loginInterceptor;

    @Autowired
    private RoleInterceptor roleInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        //登录和注册不拦截，放行
        registry.addInterceptor(loginInterceptor)
                .excludePathPatterns(
                        "/user/login",
                        "/user/register",
                        "/upload",
                        "/api/upload",  // 添加带 /api 前缀的上传接口
                        "/manufacturer/list",  // 生产商列表公开访问
                        "/manufacturer/*/detail"  // 生产商详情公开访问
                );

        // 添加角色权限拦截器
        registry.addInterceptor(roleInterceptor)
                .excludePathPatterns(
                        "/user/login",
                        "/user/register",
                        "/upload",
                        "/api/upload",
                        "/manufacturer/list",  // 生产商列表公开访问
                        "/manufacturer/*/detail"  // 生产商详情公开访问
                );
    }
}