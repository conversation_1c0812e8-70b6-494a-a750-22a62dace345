<template>
  <div class="reset-password-page">
    <RtHeader title="修改密码" />
    <div class="reset-password-container">
      <el-card class="reset-password-card">
        <template #header>
          <div class="header">
            <span class="title">修改密码</span>
            <span class="subtitle">为了您的账户安全，请定期修改密码</span>
          </div>
        </template>

        <el-form
          :model="passwordForm"
          :rules="rules"
          ref="formRef"
          label-width="100px"
          class="password-form"
        >
          <el-form-item label="原密码" prop="oldPassword">
            <el-input
              v-model="passwordForm.oldPassword"
              type="password"
              placeholder="请输入原密码"
              show-password
              clearable
            />
          </el-form-item>

          <el-form-item label="新密码" prop="newPassword">
            <el-input
              v-model="passwordForm.newPassword"
              type="password"
              placeholder="请输入新密码"
              show-password
              clearable
            />
          </el-form-item>

          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input
              v-model="passwordForm.confirmPassword"
              type="password"
              placeholder="请再次输入新密码"
              show-password
              clearable
            />
          </el-form-item>

          <div class="password-tips">
            <h4>密码要求：</h4>
            <ul>
              <li>密码长度为5-16位</li>
              <li>不能包含空格</li>
              <li>建议包含字母、数字和特殊字符</li>
            </ul>
          </div>

          <div class="form-actions">
            <el-button @click="handleCancel">取消</el-button>
            <el-button
              type="primary"
              @click="handleSubmit"
              :loading="loading"
            >
              确认修改
            </el-button>
          </div>
        </el-form>
      </el-card>
    </div>
    <RtTabBar />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import RtHeader from './RtHeader.vue'
import RtTabBar from '@/components/RtTabBar.vue'
import { userPasswordUpdateService } from '@/api/user'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()
const formRef = ref(null)
const loading = ref(false)

// 表单数据
const passwordForm = ref({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 自定义验证规则
const validateConfirmPassword = (rule, value, callback) => {
  if (value !== passwordForm.value.newPassword) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

// 表单验证规则
const rules = {
  oldPassword: [
    { required: true, message: '请输入原密码', trigger: 'blur' },
    { pattern: /^\S{5,16}$/, message: '密码长度为5-16位，不能包含空格', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { pattern: /^\S{5,16}$/, message: '密码长度为5-16位，不能包含空格', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        loading.value = true

        // 构建请求参数，按照后端接口要求
        const requestData = {
          old_pwd: passwordForm.value.oldPassword,
          new_pwd: passwordForm.value.newPassword,
          re_pwd: passwordForm.value.confirmPassword
        }

        const response = await userPasswordUpdateService(requestData)

        if (response.code === 0) {
          ElMessage.success('密码修改成功，请重新登录')

          // 清除用户信息和token
          userStore.logout()

          // 跳转到登录页
          router.push('/login')
        } else {
          ElMessage.error(response.message || '密码修改失败')
        }
      } catch (error) {
        console.error('修改密码失败:', error)
        if (error.response?.data?.message) {
          ElMessage.error(error.response.data.message)
        } else {
          ElMessage.error('密码修改失败，请稍后重试')
        }
      } finally {
        loading.value = false
      }
    }
  })
}

// 取消操作
const handleCancel = () => {
  ElMessageBox.confirm(
    '确定要取消修改密码吗？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '继续修改',
      type: 'warning',
    }
  ).then(() => {
    router.back()
  }).catch(() => {
    // 用户选择继续修改，不做任何操作
  })
}
</script>

<style lang="scss" scoped>
.reset-password-page {
  min-height: 100vh;
  background: #f6f8fa;
}

.reset-password-container {
  padding: 20px;
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.reset-password-card {
  width: 100%;
  max-width: 500px;
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 2px 12px #e3f2fd;

  .header {
    text-align: center;

    .title {
      display: block;
      font-size: 20px;
      font-weight: bold;
      color: #1565c0;
      margin-bottom: 8px;
    }

    .subtitle {
      font-size: 14px;
      color: #666;
    }
  }
}

.password-form {
  padding: 20px 0;

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #333;
  }

  :deep(.el-input__inner) {
    border-radius: 8px;
  }
}

.password-tips {
  background: #f8fafc;
  border: 1px solid #e3f2fd;
  border-radius: 8px;
  padding: 16px;
  margin: 20px 0;

  h4 {
    margin: 0 0 12px 0;
    font-size: 14px;
    color: #1565c0;
    font-weight: 600;
  }

  ul {
    margin: 0;
    padding-left: 20px;

    li {
      font-size: 13px;
      color: #666;
      line-height: 1.6;
      margin-bottom: 4px;
    }
  }
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 30px;

  .el-button {
    min-width: 100px;
    border-radius: 20px;
    font-weight: 500;
  }
}

:deep(.el-card) {
  --el-card-border-color: #e3f2fd;
}

:deep(.el-button--primary) {
  background: linear-gradient(135deg, #2193b0 0%, #1565c0 100%);
  border: none;

  &:hover {
    background: linear-gradient(135deg, #1565c0 0%, #0d47a1 100%);
  }
}

@media (max-width: 600px) {
  .reset-password-container {
    padding: 16px;
  }

  .reset-password-card {
    margin: 0;
  }

  .password-form {
    padding: 16px 0;
  }

  .form-actions {
    flex-direction: column;

    .el-button {
      width: 100%;
    }
  }
}
</style>