import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import RtHeader from './views/user/RtHeader.vue'
import RtTabBar from './components/RtTabBar.vue' 
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import './assets/images/main.scss'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'

const app = createApp(App)
const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)

app.component('RtHeader', RtHeader)
app.component('RtTabBar', RtTabBar) 
app.use(router)
app.use(ElementPlus)
app.use(pinia)
app.mount('#app')
