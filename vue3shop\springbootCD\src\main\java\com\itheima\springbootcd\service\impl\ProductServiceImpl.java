package com.itheima.springbootcd.service.impl;

import com.itheima.springbootcd.pojo.Product;
import com.itheima.springbootcd.mapper.ProductMapper;
import com.itheima.springbootcd.pojo.Result;
import com.itheima.springbootcd.service.IProductService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@Service
public class ProductServiceImpl implements IProductService {
    @Resource
    private ProductMapper productMapper;

    /**
     * 商品列表
     * @return
     */
    @Override
    public List<Product> list() {
        return productMapper.list();
    }

    /**
     * 新增/插入
     * @param entity
     * @return
     */
    @Override
    public Result insert(Product entity) {
        Result checkResult = check(entity);
        System.out.println(entity);
        if (checkResult.isSuccess()==0) {
            productMapper.insert(entity);
            return Result.success();
        } else {
            return checkResult;
        }
    }

    /**
     * 根据id更新
     * @param entity
     * @return
     */
    @Override
    public Result updateById(Product entity) {
        Result checkResult = check(entity);
        if (checkResult.isSuccess()==0) {
            productMapper.updateById(entity);
            return Result.success();
        } else {
            return checkResult;
        }
    }

    /**
     * 多个删除
     * @param ids
     */
    @Override
    public void removeByIds(List<Integer> ids) {
        for (Integer id : ids) {
            removeById(id);
        }
    }

    /**
     * 删除
     * @param id
     */
    @Override
    public void removeById(Integer id) {
        productMapper.deleteById(id);
    }

    /**
     * 检测是否用户名重复
     * @param entity
     * @return
     */
    private Result check(Product entity) {
        Product product = productMapper.selectByName(entity.getProductName());
        if (product != null && product.getProductId() != entity.getProductId()) {
            return Result.error("用户名已存在");
        } else {
            return Result.success();
        }
    }

    /**
     * 查找商品名字
     * @param productName
     * @return
     */
    @Override
    public Product findByProductName(String productName) {
        return productMapper.selectByName(productName);
    }

    /**
     * 根据id查询
     * @param id
     * @return
     */
    @Override
    public Product selectById(Integer id) {
        return productMapper.selectById(id);
    }

    @Override
    public List<Product> listByCategoryId(Integer categoryId) {
        return productMapper.listByCategoryId(categoryId);
    }

    @Override
    public List<Product> listByManufacturerId(Integer manufacturerId) {
        return productMapper.listByManufacturerId(manufacturerId);
    }
}

