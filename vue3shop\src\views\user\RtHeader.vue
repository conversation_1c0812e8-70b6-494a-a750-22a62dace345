<template>
  <div class="container">
    <div class="logo">
      <img :src="logo" />
      <span class="logo-text">鲸宝</span>
    </div>
    <div class="myText">我的鲸宝</div>
    <div class="buttons">
      <button class="cart-btn" @click="goCart">我的购物车</button>
      <button class="logout-btn" @click="handleLogout">退出登录</button>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import logo from '@/assets/images/my/鲸鱼.svg'
import { ElMessage } from 'element-plus'

const router = useRouter()
const userStore = useUserStore()

function goCart() {
  if (!userStore.token) {
    ElMessage.warning('请先登录')
    router.push('/login')
    return
  }
  router.push('/cart')
}

function handleLogout() {
  userStore.logout()
  router.push('/login')
}
</script>

<style scoped>
.container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(90deg, #e0f7fa 0%, #b2ebf2 100%);
  padding: 0 24px;
  height: 64px;
  border-radius: 0 0 16px 16px;
  box-shadow: 0 2px 8px rgba(33,147,176,0.08);
}
.logo {
  display: flex;
  align-items: center;
}
.logo img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #fff;
  box-shadow: 0 1px 4px #b2ebf2;
}
.logo-text {
  font-size: 22px;
  font-weight: 700;
  color: rgb(50, 111, 209);
  margin-left: 10px;
  letter-spacing: 1px;
}
.myText {
  font-size: 18px;
  font-weight: 500;
  color: #7c3ece;
  margin-left: 24px;
  flex: 1;
  text-align: center;
}
.buttons {
  display: flex;
  gap: 12px;
}
.cart-btn, .logout-btn {
  background: #fff;
  color: #2193b0;
  border: none;
  border-radius: 18px;
  font-size: 15px;
  font-weight: 600;
  padding: 8px 22px;
  cursor: pointer;
  box-shadow: 0 1px 4px #b2ebf2;
  transition: background 0.2s, color 0.2s;
}
.cart-btn:hover, .logout-btn:hover {
  background: #2193b0;
  color: #fff;
}
.logout-btn {
  background: #ffebee;
  color: #e53935;
}
.logout-btn:hover {
  background: #e53935;
  color: #fff;
}
@media (max-width: 600px) {
  .container {
    flex-direction: column;
    height: auto;
    padding: 12px;
    border-radius: 0 0 10px 10px;
  }
  .myText {
    margin: 8px 0;
    text-align: center;
  }
  .buttons {
    width: 100%;
    flex-direction: column;
    gap: 8px;
  }
  .cart-btn, .logout-btn {
    width: 100%;
  }
}
</style>