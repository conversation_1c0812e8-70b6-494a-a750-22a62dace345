package com.itheima.springbootcd.controller;



import com.itheima.springbootcd.pojo.Collect;
import com.itheima.springbootcd.pojo.Result;
import com.itheima.springbootcd.service.CollectService;
import com.itheima.springbootcd.utils.ThreadLocalUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/collect")
public class CollectController {

    @Autowired
    private CollectService collectService;

    @PostMapping
    public Result add(@RequestBody Collect collect) {
        Map<String, Object> map = ThreadLocalUtil.get();
        Integer userId = (Integer) map.get("id");
        collect.setUserId(userId);

        // 检查是否已收藏
        if (collectService.checkCollect(userId, collect.getProductId())) {
            return Result.error("该商品已收藏");
        }

        collectService.add(collect);
        return Result.success();
    }

    @DeleteMapping("/{id}")
    public Result delete(@PathVariable Integer id) {
        Map<String, Object> map = ThreadLocalUtil.get();
        Integer userId = (Integer) map.get("id");
        collectService.delete(id, userId);
        return Result.success();
    }

    @GetMapping("/list")
    public Result<List<Collect>> list(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "8") Integer pageSize) {
        Map<String, Object> map = ThreadLocalUtil.get();
        Integer userId = (Integer) map.get("id");

        List<Collect> list = collectService.list(userId, page, pageSize);
        Integer total = collectService.count(userId);

        return Result.success(list, total);
    }

    @GetMapping("/check/{productId}")
    public Result<Boolean> checkCollect(@PathVariable Integer productId) {
        Map<String, Object> map = ThreadLocalUtil.get();
        Integer userId = (Integer) map.get("id");
        boolean isCollected = collectService.checkCollect(userId, productId);
        return Result.success(isCollected);
    }
}
