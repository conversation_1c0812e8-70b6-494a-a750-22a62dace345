<script setup>
import { Plus, Upload } from '@element-plus/icons-vue'
import {ref} from 'vue'
import avatar from '@/assets/default.png'
import useUserInfoStore from '@/stores/userInfo'
import { ElMessage } from 'element-plus'

const userInfoStore = useUserInfoStore()
const uploadRef = ref()

//用户头像地址
const imgUrl = ref(userInfoStore.info.avatar || avatar)

//图片上传成功回调函数
const uploadSuccess = (response) => {
    console.log('上传响应:', response)
    if (response.code === 0) {
        imgUrl.value = response.data
        // 更新用户信息中的头像
        userInfoStore.setInfo({
            ...userInfoStore.info,
            avatar: response.data
        })
        ElMessage.success('头像上传成功')
    } else {
        ElMessage.error(response.message || '上传失败')
    }
}

// 上传失败回调
const uploadError = (error) => {
    console.error('上传失败:', error)
    ElMessage.error('上传失败，请重试')
}

// 上传前验证
const beforeUpload = (file) => {
    console.log('准备上传文件:', file)
    const isImage = file.type.startsWith('image/')
    const isLt2M = file.size / 1024 / 1024 < 2

    if (!isImage) {
        ElMessage.error('只能上传图片文件!')
        return false
    }
    if (!isLt2M) {
        ElMessage.error('图片大小不能超过 2MB!')
        return false
    }
    return true
}
import {userAvatarUpdateService} from '@/api/user'
//头像修改
const updateAvatar = async () => {
    //调用接口
    let result = await userAvatarUpdateService(imgUrl.value)
    ElMessage.success(result.msg?result.msg:'头像修改成功')
    //修改pinia中的头像
    userInfoStore.info.avatar = imgUrl.value
}
</script>

<template>
    <el-card class="page-container">
        <template #header>
            <div class="header">
                <span>更换头像</span>
            </div>
        </template>
        <el-row>
            <el-col :span="12">
                <el-upload 
                    ref="uploadRef"
                    class="avatar-uploader" 
                    :show-file-list="false"
                    :auto-upload="true"
                    action="http://localhost:8080/upload"
                    name="image"
                    :on-success="uploadSuccess"
                    :on-error="uploadError"
                    :before-upload="beforeUpload"
                    >
                    <img v-if="imgUrl" :src="imgUrl" class="avatar" />
                    <img v-else :src="avatar" width="278" />
                </el-upload>
                <br />
                <el-button type="primary" :icon="Plus" size="large"  @click="uploadRef.$el.querySelector('input').click()">
                    选择图片
                </el-button>
                <el-button type="success" :icon="Upload" size="large" @click="updateAvatar">
                    上传头像
                </el-button>
            </el-col>
        </el-row>
    </el-card>
</template>

<style lang="scss" scoped>
.avatar-uploader {
    :deep() {
        .avatar {
            width: 278px;
            height: 278px;
            display: block;
        }

        .el-upload {
            border: 1px dashed var(--el-border-color);
            border-radius: 6px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            transition: var(--el-transition-duration-fast);
        }

        .el-upload:hover {
            border-color: var(--el-color-primary);
        }

        .el-icon.avatar-uploader-icon {
            font-size: 28px;
            color: #8c939d;
            width: 278px;
            height: 278px;
            text-align: center;
        }
    }
}
</style>