package com.itheima.springbootcd.mapper;

import com.itheima.springbootcd.pojo.Cart;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
@Mapper
public interface CartMapper extends BaseMapper<Cart> {
    // 根据用户ID和商品ID查询购物车项
    Cart selectByUserAndProduct(@Param("userId") Integer userId,
                              @Param("productId") Integer productId);
    
    // 批量删除购物车项
    int batchDelete(@Param("ids") List<Long> ids);
}

