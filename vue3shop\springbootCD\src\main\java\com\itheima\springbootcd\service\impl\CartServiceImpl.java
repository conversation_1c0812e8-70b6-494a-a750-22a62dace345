package com.itheima.springbootcd.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.itheima.springbootcd.pojo.Cart;
import com.itheima.springbootcd.mapper.CartMapper;
import com.itheima.springbootcd.pojo.Product;
import com.itheima.springbootcd.service.ICartService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.itheima.springbootcd.service.IProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@Service
public class CartServiceImpl extends ServiceImpl<CartMapper, Cart> implements ICartService {
    @Autowired
    private IProductService  productService;
    
    @Override
    public boolean addToCart(Cart cart) {
        // 检查是否已存在相同商品
        LambdaQueryWrapper<Cart> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Cart::getUserId, cart.getUserId())
                   .eq(Cart::getProductId, cart.getProductId());
        Cart existCart = this.getOne(queryWrapper);
        
        if(existCart != null) {
            // 已存在则增加数量
            existCart.setQuantity(existCart.getQuantity() + cart.getQuantity());
            return this.updateById(existCart);
        } else {
            // 不存在则新增
            return this.save(cart);
        }
    }

    @Override
    public boolean updateQuantity(Integer cartId, Integer quantity) {
        Cart cart = this.getById(cartId);
        if(cart != null) {
            cart.setQuantity(quantity);
            return this.updateById(cart);
        }
        return false;
    }

    @Override
    public boolean removeCartItem(Integer cartId) {
        return this.removeById(cartId);
    }

    // ... existing code ...
    @Override
    public List<Cart> getCartList(Long userId) {
        // 使用 MyBatis-Plus 的关联查询
        LambdaQueryWrapper<Cart> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Cart::getUserId, userId.intValue());

        // 获取购物车列表
        List<Cart> cartList = this.list(queryWrapper);

        // 获取每个商品的详细信息
        for (Cart cart : cartList) {
            // 查询商品信息
            Product product = productService.selectById(cart.getProductId());
            if (product != null) {
                // 设置商品信息到购物车对象
                cart.setProductName(product.getProductName());
                cart.setProductImage(product.getImagePath());
                cart.setPrice(product.getPrice().doubleValue());
            }
        }

        return cartList;
    }
// ... existing code ...

    @Override
    public boolean removeCartItem(Long cartId) {
        return this.removeById(cartId);
    }

    @Override
    public boolean clearCart(Long userId) {
        LambdaQueryWrapper<Cart> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Cart::getUserId, userId);
        return this.remove(queryWrapper);
    }

}
