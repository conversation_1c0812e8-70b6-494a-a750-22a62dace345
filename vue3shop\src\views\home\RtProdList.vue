<template>
  <div class="product-list">
    <el-row :gutter="20">
      <el-col :span="6" v-for="product in productStore.products" :key="product.id">
        <el-card :body-style="{ padding: '0px' }" shadow="hover" @click="gotoProductDetail(product)">
          <img :src="product.imgUrl" class="product-image" alt="Product Image" />
          <div style="padding: 14px;">
            <p class="product-name">{{ product.name }}</p>
            <div class="bottom">
              <span class="price">￥{{ product.price }}</span>
              <el-button type="text" class="button">查看详情</el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { useProductStore } from '@/stores/product'

const router = useRouter()
const productStore = useProductStore()

// 请求商品列表
productStore.getProductList()

const gotoProductDetail = (product) => {
  // 跳转到商品详情页面，并传递商品 id
  router.push({ name: 'ProductDetail', params: { id: product.id } })
}
</script>

<style scoped>
.product-list {
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 2px 12px #e3f2fd;
  /* padding: 22px 18px 18px 18px; */
  margin-bottom: 18px;
  min-width: 0;
}

.el-card {
  background-color: #f8fafc;
  width: 280px;
  height: 300px;
  text-align: center;
  border-radius: 10px;
  margin:22px;
  box-shadow: 0 4px 8px #eef3f8;
}

.product-image {
  width: 90%;
  height: 180px;
  display: block;
  margin: auto;
}

.product-name {
  margin: 10px 0;
  font-size: 16px;
  font-size: 20px;
  color: #1565c0;
  font-weight: 600;
  text-align: center;
}

.bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}

.price {
  font-size: 20px;
  color: #ff4500;
}

.button {
  padding: 5px 10px;
}
</style>
