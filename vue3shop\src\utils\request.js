import axios from 'axios';
import { ElMessage } from 'element-plus';
// import { useRouter } from 'vue-router';
// const router = useRouter();
import router from '@/router';
// 创建 axios 实例
const request = axios.create({
    baseURL: '/api',
    timeout: 5000
});

// 请求拦截器
request.interceptors.request.use(
    config => {
        // 从 localStorage 直接获取 token
        const token = localStorage.getItem('user-store') ? 
            JSON.parse(localStorage.getItem('user-store')).token : '';
        console.log('当前token:', token);
        
        if (token) {
            config.headers['Authorization'] = `Bearer ${token}`;
            console.log('请求头:', config.headers);
        }
        return config;
    },
    error => {
        console.error('请求错误:', error);
        return Promise.reject(error);
    }
);

// 响应拦截器
request.interceptors.response.use(
    response => {
        console.log('响应数据:', response.data);
        return response.data;
    },
    error => {
        console.log('错误响应:', error.response);
        if (error.response) {
            switch (error.response.status) {
                case 401:
                    // 清除本地存储的 token
                    localStorage.removeItem('user-store');
                    ElMessage.error('登录已过期，请重新登录');
                    router.push('/login');
                    break;
                case 403:
                    ElMessage.error('没有权限访问');
                    break;
                case 404:
                    ElMessage.error('请求的资源不存在');
                    break;
                case 500:
                    ElMessage.error('服务器错误');
                    break;
                default:
                    ElMessage.error(error.response.data?.message || '请求失败');
            }
        } else {
            ElMessage.error('网络错误，请检查网络连接');
        }
        return Promise.reject(error);
    }
);

export default request;