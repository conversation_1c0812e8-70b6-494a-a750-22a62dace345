package com.itheima.springbootcd.service;

import com.itheima.springbootcd.pojo.User;

public interface UserService {
    User findByUsername(String username);

    void register(String username, String password);

    void register(String username, String password, String role);

    void update(User user);

    void updateAvatar(String avatarUrl);

    void deleteById(Integer userId);

    void updatePwd(String newPwd);
}
