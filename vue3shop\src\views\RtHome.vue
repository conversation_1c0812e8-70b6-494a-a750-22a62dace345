<template>
  <div>
    <RtHeader title="首页" />
    <div class="main-row">
      <RtMyInfo />
      <RtSwipe />
      <RtNav />
    </div>
    <RtAdProd class="ad-prod-large" />
    <RtProdList/>
    <RtTabBar />
  </div>
</template>

<script setup>
import RtSwipe from './home/<USER>'
import RtNav from './home/<USER>'
import RtMyInfo from './home/<USER>'
import RtAdProd from './home/<USER>'
import RtProdList from './home/<USER>'
</script>

<style scoped>

.main-row {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: stretch;
  gap: 64px;
  margin: 64px 0 24px 60px;
  width: 100%;
  max-width: 1200px;
  box-sizing: border-box;
}
.main-row > *:nth-child(1) {
  flex: 1 1 0%;
  min-width: 220px;
  max-width: 320px;
  align-self: flex-start;
}
.main-row > *:nth-child(2) {
  flex: 2 1 0%;
  min-width: 340px;
  max-width: 480px;
  align-self: flex-start;
}
.main-row > *:nth-child(3) {
  flex: 1 1 0%;
  min-width: 140px;
  max-width: 320px;
  align-self: flex-start;
}
.ad-prod-large {
  width: 100%;
  max-width: 700px;
  margin: 32px auto 32px auto;
  display: flex;
  justify-content: center;
}
@media (max-width: 1100px) {
  .main-row {
    flex-direction: column;
    gap: 18px;
    align-items: stretch;
    max-width: 98vw;
    margin: 24px 0 18px 0;
  }
  .main-row > * {
    max-width: unset;
    min-width: unset;
    width: 100%;
  }
  .ad-prod-large {
    max-width: 98vw;
    margin: 18px auto 18px auto;
  }
}
body {
  background: #f6f8fa;
}
</style>
