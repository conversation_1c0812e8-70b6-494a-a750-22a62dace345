package com.itheima.springbootcd.interceptors;

import com.itheima.springbootcd.annotation.RequireRole;
import com.itheima.springbootcd.enums.UserRole;
import com.itheima.springbootcd.pojo.User;
import com.itheima.springbootcd.service.UserService;
import com.itheima.springbootcd.utils.ThreadLocalUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import java.util.Arrays;
import java.util.Map;

/**
 * 角色权限拦截器
 */
@Component
public class RoleInterceptor implements HandlerInterceptor {

    @Autowired
    private UserService userService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 只处理方法级别的请求
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;
        
        // 检查方法上是否有RequireRole注解
        RequireRole methodAnnotation = handlerMethod.getMethodAnnotation(RequireRole.class);
        // 检查类上是否有RequireRole注解
        RequireRole classAnnotation = handlerMethod.getBeanType().getAnnotation(RequireRole.class);
        
        RequireRole requireRole = methodAnnotation != null ? methodAnnotation : classAnnotation;
        
        // 如果没有权限注解，直接放行
        if (requireRole == null) {
            return true;
        }

        // 获取当前用户信息
        Map<String, Object> claims = ThreadLocalUtil.get();
        if (claims == null) {
            response.setStatus(401);
            response.getWriter().write("{\"code\":1,\"message\":\"用户未登录\"}");
            return false;
        }

        Integer userId = (Integer) claims.get("id");
        String username = (String) claims.get("username");
        
        // 获取用户详细信息（包括角色）
        User user = userService.findByUsername(username);
        if (user == null) {
            response.setStatus(401);
            response.getWriter().write("{\"code\":1,\"message\":\"用户不存在\"}");
            return false;
        }

        // 获取用户角色
        String userRoleStr = user.getRole();
        if (userRoleStr == null) {
            userRoleStr = "CUSTOMER"; // 默认为普通用户
        }
        
        UserRole userRole = UserRole.fromCode(userRoleStr);
        
        // 检查是否是管理员（管理员默认有所有权限）
        if (requireRole.allowAdmin() && userRole == UserRole.ADMIN) {
            return true;
        }
        
        // 检查用户角色是否在允许的角色列表中
        UserRole[] allowedRoles = requireRole.value();
        boolean hasPermission = Arrays.asList(allowedRoles).contains(userRole);
        
        if (!hasPermission) {
            response.setStatus(403);
            response.getWriter().write("{\"code\":1,\"message\":\"权限不足\"}");
            return false;
        }

        return true;
    }
}
