# DIY电脑配置页面

## 功能概述

这个页面允许用户通过选择不同分类的商品来配置自己的DIY电脑，包括：

- **配置名称**: 手动输入配置方案的名称
- **CPU**: 从数据库中的CPU分类商品中选择
- **显卡**: 从数据库中的显卡分类商品中选择
- **主板**: 从数据库中的主板分类商品中选择
- **内存**: 从数据库中的内存分类商品中选择
- **存储**: 从数据库中的存储分类商品中选择
- **电源**: 从数据库中的电源分类商品中选择
- **机箱**: 从数据库中的机箱分类商品中选择
- **散热器**: 从数据库中的散热器分类商品中选择

## 主要特性

### 1. 智能商品加载
- 当用户点击下拉菜单时，自动从后端加载对应分类的商品
- 支持商品名称和价格的显示
- 支持搜索过滤功能

### 2. 实时价格计算
- 自动计算所选配件的总价
- 实时更新显示，方便用户控制预算

### 3. 表单验证
- 必填项验证
- 预算范围验证（最低1000元）
- 配置名称长度验证

### 4. 用户体验优化
- 响应式设计，支持移动端
- 加载状态提示
- 美观的渐变背景和卡片设计
- 提交确认对话框

## 技术实现

### 前端技术栈
- **Vue 3**: 使用Composition API
- **Element Plus**: UI组件库
- **Pinia**: 状态管理
- **Vue Router**: 路由管理
- **Axios**: HTTP请求

### 后端接口依赖
- `GET /category`: 获取分类列表
- `GET /product/listByCategoryId/{categoryId}`: 根据分类ID获取商品列表
- `POST /diy-config`: 保存DIY配置（需要实现）

### 数据结构

#### 分类数据结构
```javascript
{
  categoryId: Integer,
  categoryName: String,
  description: String,
  createTime: String,
  updateTime: String
}
```

#### 商品数据结构
```javascript
{
  productId: Integer,
  productName: String,
  categoryId: Integer,
  price: BigDecimal,
  stockQuantity: Integer,
  description: String,
  imagePath: String
}
```

#### DIY配置数据结构
```javascript
{
  configName: String,   // 配置名称
  cpu: Integer,         // CPU商品ID
  gpu: Integer,         // 显卡商品ID
  motherboard: Integer, // 主板商品ID
  memory: Integer,      // 内存商品ID
  storage: Integer,     // 存储商品ID
  power: Integer,       // 电源商品ID
  case: Integer,        // 机箱商品ID
  cooler: Integer,      // 散热器商品ID
  budget: Number,       // 预算
  remarks: String,      // 备注
  userId: Integer,      // 用户ID
  createTime: String    // 创建时间
}
```

## 使用说明

### 1. 访问页面
- 路由路径: `/diy-computer`
- 需要用户登录才能访问

### 2. 配置流程
1. 输入配置名称
2. 选择CPU（点击下拉菜单会自动加载CPU分类的商品）
3. 选择显卡（点击下拉菜单会自动加载显卡分类的商品）
4. 依次选择其他配件（主板、内存、存储、电源、机箱、散热器）
5. 查看实时计算的总价
6. 设置预算范围
7. 添加备注（可选）
8. 点击"提交配置"保存

### 3. 注意事项
- 确保后端数据库中有对应的分类和商品数据
- 分类名称必须与代码中的匹配（CPU、显卡、主板、内存、存储、电源、机箱、散热器）
- 需要实现后端的DIY配置保存接口
- 所有配件选择都是基于商品ID，而不是文本输入

## 后端接口要求

### 需要实现的接口

#### 保存DIY配置
```
POST /diy-config
Content-Type: application/json
Authorization: Bearer {token}

Request Body:
{
  "configName": "我的游戏配置",
  "cpu": 1,           // CPU商品ID
  "gpu": 2,           // 显卡商品ID
  "motherboard": 3,   // 主板商品ID
  "memory": 4,        // 内存商品ID
  "storage": 5,       // 存储商品ID
  "power": 6,         // 电源商品ID
  "case": 7,          // 机箱商品ID
  "cooler": 8,        // 散热器商品ID
  "budget": 15000,
  "remarks": "高性能游戏配置",
  "userId": 1,
  "createTime": "2024-01-01T00:00:00.000Z"
}

Response:
{
  "code": 0,
  "message": "success",
  "data": null
}
```

## 开发和部署

### 开发环境运行
```bash
# 前端
cd vue3shop
npm run dev

# 后端
cd springbootCD
# 使用IDE运行SpringBoot应用
```

### 访问地址
- 前端: http://localhost:8081
- 后端: http://localhost:8080
- DIY配置页面: http://localhost:8081/diy-computer

## 扩展功能建议

1. **配置保存和管理**: 用户可以保存多个配置方案
2. **配置分享**: 用户可以分享自己的配置给其他人
3. **兼容性检查**: 检查选择的配件是否兼容
4. **性能评估**: 根据配置给出性能评分
5. **价格趋势**: 显示配件的价格变化趋势
6. **推荐系统**: 根据预算推荐最优配置
