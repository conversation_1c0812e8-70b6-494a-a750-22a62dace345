package com.itheima.springbootcd.service;

import com.itheima.springbootcd.pojo.Product;
import com.baomidou.mybatisplus.extension.service.IService;
import com.itheima.springbootcd.pojo.Result;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
public interface IProductService{
    List<Product> list();

    Result insert(Product entity);

    Result updateById(Product entity);

    void removeByIds(List<Integer> ids);

    void removeById(Integer id);

    Product findByProductName(String productName);

    Product selectById(Integer id);

    public List<Product> listByCategoryId(Integer categoryId);

    public List<Product> listByManufacturerId(Integer manufacturerId);
}
