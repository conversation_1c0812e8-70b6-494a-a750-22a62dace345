package com.itheima.springbootcd.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@Getter
@Setter
@ToString
@TableName("product")
public class Product implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "product_id", type = IdType.AUTO)
    private Integer productId;

    @TableField("product_name")
    private String productName;

    @TableField("category_id")
    private Integer categoryId;

    @TableField("price")
    private BigDecimal price;

    @TableField("stock_quantity")
    private Integer stockQuantity;

    @TableField("description")
    private String description;

    @TableField("image_path")
    private String imagePath;
}
