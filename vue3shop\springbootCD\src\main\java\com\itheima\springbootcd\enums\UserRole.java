package com.itheima.springbootcd.enums;

/**
 * 用户角色枚举
 */
public enum UserRole {
    CUSTOMER("CUSTOMER", "普通用户"),
    MANUFACTURER("MANUFACTURER", "生产商"),
    ADMIN("ADMIN", "管理员");

    private final String code;
    private final String description;

    UserRole(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static UserRole fromCode(String code) {
        for (UserRole role : UserRole.values()) {
            if (role.getCode().equals(code)) {
                return role;
            }
        }
        return CUSTOMER; // 默认为普通用户
    }
}
