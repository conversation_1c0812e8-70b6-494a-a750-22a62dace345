# 权限管理系统说明

## 系统概述

本项目实现了基于角色的权限控制系统（RBAC），支持三种用户角色：
- **CUSTOMER（普通用户）**：只能浏览和购买商品
- **MANUFACTURER（生产商）**：可以上架和管理自己的商品
- **ADMIN（管理员）**：拥有所有权限

## 权限设计

### 角色权限矩阵

| 功能 | 普通用户 | 生产商 | 管理员 |
|------|----------|--------|--------|
| 浏览商品 | ✅ | ✅ | ✅ |
| 购买商品 | ✅ | ✅ | ✅ |
| 浏览生产商信息 | ✅ | ✅ | ✅ |
| 添加商品 | ❌ | ✅ | ✅ |
| 编辑商品 | ❌ | ✅（仅自己的） | ✅ |
| 删除商品 | ❌ | ✅（仅自己的） | ✅ |
| 管理生产商信息 | ❌ | ✅（仅自己的） | ✅ |
| 添加生产商 | ❌ | ❌ | ✅ |
| 删除生产商 | ❌ | ❌ | ✅ |

## 技术实现

### 1. 数据库设计

#### 用户表更新
```sql
ALTER TABLE user ADD COLUMN role VARCHAR(20) DEFAULT 'CUSTOMER';
ALTER TABLE user ADD COLUMN manufacturer_id INT DEFAULT NULL;
```

#### 生产商表
```sql
CREATE TABLE manufacturer (
    manufacturer_id INT AUTO_INCREMENT PRIMARY KEY,
    manufacturer_name VARCHAR(100) NOT NULL,
    description TEXT,
    logo_url VARCHAR(255),
    contact_info VARCHAR(255),
    address VARCHAR(255),
    website VARCHAR(255),
    status INT DEFAULT 0,
    create_time DATETIME NOT NULL,
    update_time DATETIME NOT NULL,
    create_user INT NOT NULL
);
```

#### 商品表更新
```sql
ALTER TABLE product ADD COLUMN manufacturer_id INT DEFAULT NULL;
```

### 2. 后端实现

#### 角色枚举
```java
public enum UserRole {
    CUSTOMER("CUSTOMER", "普通用户"),
    MANUFACTURER("MANUFACTURER", "生产商"),
    ADMIN("ADMIN", "管理员");
}
```

#### 权限注解
```java
@RequireRole({UserRole.MANUFACTURER, UserRole.ADMIN})
public Result addProduct(@RequestBody Product product) {
    // 只有生产商和管理员可以添加商品
}
```

#### 权限拦截器
- `LoginInterceptor`：验证用户是否登录
- `RoleInterceptor`：验证用户是否有相应角色权限

### 3. 前端实现

#### 路由权限
```javascript
// 公开访问的路由
{
  path: "/manufacturer",
  component: ManufacturerList,
  // 无需登录即可访问
}

// 需要登录的路由
{
  path: "/product/manage",
  component: ProductManage,
  meta: { requiresAuth: true }
}
```

## 使用指南

### 1. 数据库初始化

执行 `database_migration.sql` 文件来初始化权限系统：

```bash
mysql -u root -p your_database < database_migration.sql
```

### 2. 用户注册

用户注册时可以选择角色：
- 普通用户：默认角色，可以浏览和购买
- 生产商：需要额外信息，可以上架商品

### 3. 权限验证

#### 后端权限验证
使用 `@RequireRole` 注解：
```java
@RequireRole(UserRole.MANUFACTURER)
public Result addProduct() {
    // 仅生产商可访问
}

@RequireRole({UserRole.MANUFACTURER, UserRole.ADMIN})
public Result updateProduct() {
    // 生产商和管理员可访问
}
```

#### 前端权限验证
在组件中检查用户角色：
```javascript
const userStore = useUserStore()
const isManufacturer = computed(() => 
  userStore.userInfo?.role === 'MANUFACTURER'
)
```

## API 接口

### 生产商相关接口

| 方法 | 路径 | 权限 | 说明 |
|------|------|------|------|
| GET | `/manufacturer/list` | 公开 | 获取生产商列表 |
| GET | `/manufacturer/{id}/detail` | 公开 | 获取生产商详情 |
| POST | `/manufacturer/add` | 管理员 | 添加生产商 |
| PUT | `/manufacturer/update` | 生产商/管理员 | 更新生产商信息 |
| DELETE | `/manufacturer/{id}` | 管理员 | 删除生产商 |

### 商品相关接口

| 方法 | 路径 | 权限 | 说明 |
|------|------|------|------|
| GET | `/product/list` | 公开 | 获取商品列表 |
| GET | `/product/selectById/{id}` | 公开 | 获取商品详情 |
| POST | `/product/add` | 生产商/管理员 | 添加商品 |
| PUT | `/product/update` | 生产商/管理员 | 更新商品 |
| DELETE | `/product/delBatch` | 生产商/管理员 | 删除商品 |
| GET | `/product/listByManufacturerId/{id}` | 公开 | 获取生产商商品 |

## 页面功能

### 1. 生产商列表页面 (`/manufacturer`)
- 展示所有生产商的缩略信息
- 支持点击查看详情
- 公开访问，无需登录

### 2. 生产商详情页面 (`/manufacturer/{id}/detail`)
- 展示生产商详细信息
- 展示该生产商的所有商品
- 支持点击商品跳转到商品详情
- 公开访问，无需登录

### 3. 商品管理页面
- 生产商可以管理自己的商品
- 管理员可以管理所有商品
- 需要相应权限才能访问

## 安全考虑

1. **JWT Token验证**：所有需要权限的接口都需要有效的JWT Token
2. **角色验证**：通过拦截器验证用户角色
3. **数据隔离**：生产商只能操作自己的数据
4. **输入验证**：所有用户输入都进行验证
5. **错误处理**：统一的错误响应格式

## 扩展建议

1. **细粒度权限**：可以进一步细分权限，如商品分类管理权限
2. **权限缓存**：使用Redis缓存用户权限信息
3. **操作日志**：记录重要操作的日志
4. **权限审计**：定期审计用户权限
5. **动态权限**：支持运行时动态调整权限

## 故障排除

### 常见问题

1. **权限不足错误**
   - 检查用户角色是否正确
   - 确认接口是否添加了正确的权限注解

2. **登录状态丢失**
   - 检查JWT Token是否过期
   - 确认Redis中的Token是否存在

3. **角色验证失败**
   - 检查数据库中用户的role字段
   - 确认RoleInterceptor是否正确配置

### 调试建议

1. 开启详细日志记录
2. 使用浏览器开发者工具检查网络请求
3. 检查数据库中的用户角色数据
4. 验证JWT Token的有效性
