package com.itheima.springbootcd.controller;

import com.itheima.springbootcd.pojo.Category;
import com.itheima.springbootcd.pojo.Result;
import com.itheima.springbootcd.service.ICategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@RestController
@RequestMapping("/category")
public class CategoryController {
    @Autowired
    private ICategoryService categoryService;
    @PostMapping
    public Result add(@RequestBody @Validated(Category.Add.class) Category category){
        categoryService.add(category);
        return Result.success();
    }
    @GetMapping
    public Result<List<Category>> list(){
        List<Category> cs = categoryService.list();
        return Result.success(cs);
    }
    @GetMapping("/detail")
    public Result<Category> detail(Integer id){
    Category c = categoryService.findById(id);
    return Result.success(c);
    }
    @PutMapping
    public Result update(@RequestBody @Validated(Category.Update.class) Category category){
        categoryService.update(category);
        return Result.success();
    }
    @PostMapping("/delete")
    public Result delete(@RequestBody Category category) {
        categoryService.delete(category.getCategoryId());
        return Result.success();
    }
    @GetMapping("/search")
    public Result<List<Category>> search(@RequestParam String categoryName){
        List<Category> categories = categoryService.search(categoryName);
        return Result.success(categories);
    }
}
