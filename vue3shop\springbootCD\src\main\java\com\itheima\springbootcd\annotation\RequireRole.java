package com.itheima.springbootcd.annotation;

import com.itheima.springbootcd.enums.UserRole;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 角色权限注解
 * 用于标记需要特定角色才能访问的方法
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface RequireRole {
    /**
     * 需要的角色
     */
    UserRole[] value() default {};
    
    /**
     * 是否允许管理员访问（默认true）
     */
    boolean allowAdmin() default true;
}
