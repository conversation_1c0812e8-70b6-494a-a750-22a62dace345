import request from '@/utils/request'
import { useUserStore } from '@/stores/user'

// 保存DIY配置
export const saveDiyConfigService = (configData) => {
  const userStore = useUserStore()
  return request.post('/diy-config', configData, {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': userStore.token
    }
  })
}

// 获取用户的DIY配置列表
export const getDiyConfigListService = () => {
  const userStore = useUserStore()
  return request.get('/diy-config/list', {
    headers: {
      'Authorization': userStore.token
    }
  })
}

// 获取DIY配置详情
export const getDiyConfigDetailService = (configId) => {
  const userStore = useUserStore()
  return request.get(`/diy-config/${configId}`, {
    headers: {
      'Authorization': userStore.token
    }
  })
}

// 更新DIY配置
export const updateDiyConfigService = (configId, configData) => {
  const userStore = useUserStore()
  return request.put(`/diy-config/${configId}`, configData, {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': userStore.token
    }
  })
}

// 删除DIY配置
export const deleteDiyConfigService = (configId) => {
  const userStore = useUserStore()
  return request.delete(`/diy-config/${configId}`, {
    headers: {
      'Authorization': userStore.token
    }
  })
}

// 分享DIY配置
export const shareDiyConfigService = (configId) => {
  const userStore = useUserStore()
  return request.post(`/diy-config/${configId}/share`, {}, {
    headers: {
      'Authorization': userStore.token
    }
  })
}

// 获取热门DIY配置
export const getPopularDiyConfigsService = (limit = 10) => {
  return request.get('/diy-config/popular', {
    params: { limit }
  })
}
