import { defineStore } from 'pinia'
import { ref } from 'vue'
import { productCategoryListService, productCategoryAddService, productCategoryUpdateService, productCategoryDeleteService, productCategorySearchService } from '@/api/product'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'

export const useCategoryStore = defineStore('category', () => {
  const categoryList = ref([])
  const loading = ref(false)
  const userStore = useUserStore()

  // 获取分类列表
  const getCategoryList = async () => {
    if (!userStore.token) {
      console.log('未登录，无法获取分类列表')
      return false
    }

    try {
      loading.value = true
      console.log('开始获取分类列表，当前token:', userStore.token)
      const result = await productCategoryListService()
      console.log('获取分类列表结果:', result)
      if (result && result.data) {
        categoryList.value = result.data
        return true
      } else {
        console.error('获取分类列表失败: 返回数据格式不正确', result)
        return false
      }
    } catch (error) {
      console.error('获取分类列表失败:', error)
      if (error.response?.status === 401) {
        ElMessage.error('登录已过期，请重新登录')
        userStore.logout()
        return false
      }
      ElMessage.error('获取分类列表失败')
      return false
    } finally {
      loading.value = false
    }
  }

  // 添加分类
  const addCategory = async (categoryData) => {
    if (!userStore.token) {
      console.log('未登录，无法添加分类')
      ElMessage.error('请先登录')
      return false
    }

    try {
      const response = await productCategoryAddService({
        ...categoryData,
        headers: {
          'Authorization': userStore.token
        }
      })
      if (response.code === 0) {
        ElMessage.success('添加成功')
        await getCategoryList()
        return true
      } else {
        ElMessage.error(response.message || '添加失败')
        return false
      }
    } catch (error) {
      if (error.response?.status === 401) {
        ElMessage.error('登录已过期，请重新登录')
        userStore.logout()
        return false
      }
      ElMessage.error('添加失败')
      console.error('添加失败:', error)
      return false
    }
  }

  // 更新分类
  const updateCategory = async (categoryId, categoryData) => {
    if (!userStore.token) {
      console.log('未登录，无法更新分类')
      ElMessage.error('请先登录')
      return false
    }

    try {
      const response = await productCategoryUpdateService({
        ...categoryData,
        categoryId
      })
      if (response.code === 0) {
        ElMessage.success('更新成功')
        await getCategoryList()
        return true
      } else {
        ElMessage.error(response.message || '更新失败')
        return false
      }
    } catch (error) {
      if (error.response?.status === 401) {
        ElMessage.error('登录已过期，请重新登录')
        userStore.logout()
        return false
      }
      ElMessage.error('更新失败')
      console.error('更新失败:', error)
      return false
    }
  }

  // 删除分类
  const deleteCategory = async (categoryId) => {
    try {
      const response = await productCategoryDeleteService(categoryId)
      if (response.code === 0) {
        ElMessage.success('删除成功')
        await getCategoryList()
        return true
      } else {
        ElMessage.error(response.message || '删除失败')
        return false
      }
    } catch (error) {
      if (error.response?.status === 401) {
        ElMessage.error('登录已过期，请重新登录')
        userStore.logout()
        return false
      }
      ElMessage.error('删除失败')
      console.error('删除失败:', error)
      return false
    }
  }

  // 搜索分类
  const searchCategories = async (categoryName) => {
    if (!userStore.token) {
      console.log('未登录，无法搜索分类')
      ElMessage.error('请先登录')
      return false
    }

    try {
      loading.value = true
      const result = await productCategorySearchService(categoryName)
      console.log('搜索分类结果:', result)
      if (result && result.data) {
        categoryList.value = result.data
        return true
      } else {
        console.error('搜索分类失败: 返回数据格式不正确', result)
        return false
      }
    } catch (error) {
      console.error('搜索分类失败:', error)
      if (error.response?.status === 401) {
        ElMessage.error('登录已过期，请重新登录')
        userStore.logout()
        return false
      }
      ElMessage.error('搜索分类失败')
      return false
    } finally {
      loading.value = false
    }
  }

  return {
    categoryList,
    loading,
    getCategoryList,
    addCategory,
    updateCategory,
    deleteCategory,
    searchCategories
  }
})