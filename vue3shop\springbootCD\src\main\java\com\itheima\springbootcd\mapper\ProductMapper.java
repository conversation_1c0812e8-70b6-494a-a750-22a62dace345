package com.itheima.springbootcd.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.itheima.springbootcd.pojo.Product;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@Mapper
public interface ProductMapper {
    /**
     * 根据id查询
     * @param id
     * @return
     */
    @Select("SELECT * FROM product WHERE product_id=#{productId}")
    Product selectById(Integer id);

    /**
     * 根据product_name查询
     * @param name
     * @return
     */
    @Select("SELECT * FROM product WHERE product_name = #{product_username}")
    Product selectByName(String name);
    /**
     * 获取商品列表
     * @return
     */
    @Select("SELECT * FROM product")
    List<Product> list();

    /**
     * 新增/插入（添加属性未满）
     * @param entity
     * @return
     */
    @Insert("INSERT INTO product(product_name,price,stock_quantity,description,image_path,category_id)"+
            "VALUES (#{productName},#{price},#{stockQuantity},#{description},#{imagePath},#{categoryId})")
    int insert(Product entity);

    /**
     * 更新（根据id）
     * @param entity
     * @return
     */
    @Update("UPDATE product SET product_name=#{productName}, " +
            "category_id=#{categoryId}, " +
            "price=#{price}, " +
            "stock_quantity=#{stockQuantity}, " +
            "description=#{description}, " +
            "image_path=#{imagePath} " +
            "WHERE product_id=#{productId}")
    int updateById(Product entity);

    /**
     * 删除（根据id）
     * @param id
     * @return
     */
    @Delete("DELETE FROM product WHERE product_id = #{productId}")
    void deleteById(Integer id);

    /**
     * 查询分类商品列表
     * @return
     */
    @Select("SELECT * FROM product WHERE category_id = #{categoryId}")
    List<Product> listByCategoryId(Integer categoryId);

    /**
     * 根据生产商ID查询商品列表
     * @param manufacturerId
     * @return
     */
    @Select("SELECT * FROM product WHERE manufacturer_id = #{manufacturerId}")
    List<Product> listByManufacturerId(Integer manufacturerId);
}

