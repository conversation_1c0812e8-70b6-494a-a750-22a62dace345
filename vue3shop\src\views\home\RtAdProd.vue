<template>
  <div class="APContainer">
    <div class="APBlock" v-for="(product, index) in products" :key="product.id || index">
      <span class="APContainer-title">国家补贴X百亿补贴</span>
      <div class="APProd" @click="goToProductDetail(product.id)" :class="{ 'clickable': product.id }">
        <img
          :src="product.imgUrl || product.src"
          :alt="product.name || '国家补贴'"
          class="APImg"
          @error="handleImageError"
        />
        <div class="APInfo">
          <div class="APSpan">已补{{ product.discount || product.Albutie }}元</div>
          <div class="APPrice">￥{{ product.price }}</div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-text">加载中...</div>
    </div>

    <!-- 错误状态 -->
    <div v-if="error && !loading" class="error-container">
      <div class="error-text">{{ error }}</div>
      <button @click="loadProducts" class="retry-btn">重试</button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useProductStore } from '@/stores/product'
import { ElMessage } from 'element-plus'
import guobu from '@/assets/images/my/国家补贴.jpg'
import guobu2 from '@/assets/images/my/国家补贴2.jpg'

const router = useRouter()
const productStore = useProductStore()

// 响应式数据
const products = ref([])
const loading = ref(false)
const error = ref('')

// 配置要显示的产品ID（根据您的数据库中的实际产品ID进行调整）
const PRODUCT_IDS = [13,14] // 根据图片显示的产品，您可能需要调整这些ID

// 默认产品数据（作为后备）
const defaultProducts = [
  {
    id: null,
    src: guobu,
    Albutie: 1780,
    price: 4890,
    name: '国家补贴产品1'
  },
  {
    id: null,
    src: guobu2,
    Albutie: 1000,
    price: 8900,
    name: '国家补贴产品2'
  }
]

// 加载产品数据
const loadProducts = async () => {
  loading.value = true
  error.value = ''

  try {
    const loadedProducts = []

    // 尝试从数据库加载指定的产品
    for (let i = 0; i < PRODUCT_IDS.length; i++) {
      const productId = PRODUCT_IDS[i]
      try {
        const productDetail = await productStore.getProductDetail(productId)
        if (productDetail) {
          // 将数据库产品数据与默认数据合并
          loadedProducts.push({
            ...productDetail,
            discount: defaultProducts[i]?.Albutie || 1000, // 使用默认的补贴金额
            imgUrl: productDetail.images?.[0] || defaultProducts[i]?.src
          })
        } else {
          // 如果无法加载，使用默认数据
          loadedProducts.push(defaultProducts[i])
        }
      } catch (err) {
        console.warn(`无法加载产品 ${productId}:`, err)
        // 使用默认数据
        loadedProducts.push(defaultProducts[i])
      }
    }

    products.value = loadedProducts.length > 0 ? loadedProducts : defaultProducts

  } catch (err) {
    console.error('加载产品失败:', err)
    error.value = '加载产品失败，显示默认数据'
    products.value = defaultProducts
  } finally {
    loading.value = false
  }
}

// 跳转到商品详情页
const goToProductDetail = (productId) => {
  if (productId) {
    router.push({ name: 'ProductDetail', params: { id: productId } })
  } else {
    ElMessage.info('该商品暂无详情页面')
  }
}

// 图片加载错误处理
const handleImageError = (event) => {
  console.warn('图片加载失败:', event.target.src)
  // 可以设置默认图片
  event.target.src = 'https://via.placeholder.com/200x200?text=No+Image'
}

// 组件挂载时加载数据
onMounted(() => {
  loadProducts()
})
</script>

<style scoped>
.APContainer {
  display: flex;
  flex-direction: row;
  background: #fffbe7;
  border-radius: 18px;
  box-shadow: 0 2px 12px #ffe082;
  padding: 28px 38px 28px 38px;
  max-width: 900px;
  min-width: 320px;
  margin: 0 auto;
  align-items: flex-start;
  gap: 48px;
}
.APBlock {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}
.APContainer-title {
  font-size: 22px;
  font-weight: bold;
  color: #b8860b;
  margin-bottom: 18px;
  letter-spacing: 1px;
  text-align: center;
}
.APProd {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 18px;
}

.APProd.clickable {
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.APProd.clickable:hover {
  transform: translateY(-2px);
}

.APProd.clickable:hover .APImg {
  box-shadow: 0 4px 20px #ffe082;
}
.APImg {
  width: 200px;
  height: 200px;
  border-radius: 18px;
  object-fit: cover;
  box-shadow: 0 1px 12px #ffe082;
  background: #fff;
}
.APInfo {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: center;
}
.APSpan {
  font-size: 18px;
  color: #e2231a;
  font-weight: 600;
}
.APPrice {
  font-size: 28px;
  color: #d32f2f;
  font-weight: bold;
  margin-top: 4px;
}
@media (max-width: 900px) {
  .APContainer {
    flex-direction: column;
    gap: 24px;
    padding: 12px 6px;
    border-radius: 10px;
    box-shadow: none;
    max-width: 98vw;
  }
  .APBlock {
    margin-bottom: 12px;
  }
  .APImg {
    width: 100px;
    height: 100px;
  }
  .APContainer-title {
    font-size: 15px;
    margin-bottom: 8px;
  }
  .APSpan, .APPrice {
    font-size: 13px;
  }
}

/* 加载和错误状态样式 */
.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.loading-text {
  font-size: 16px;
  color: #b8860b;
  font-weight: 500;
}

.error-text {
  font-size: 14px;
  color: #e2231a;
  margin-bottom: 10px;
}

.retry-btn {
  background: #b8860b;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s ease;
}

.retry-btn:hover {
  background: #9a7209;
}
</style>