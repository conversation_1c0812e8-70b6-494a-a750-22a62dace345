<template>
  <div class="product-manage">
    <RtHeader title="商品管理" />
    <div class="product-container">
      <el-card class="product-card">
        <template #header>
          <div class="header">
            <span class="title">商品管理</span>
            <div class="extra">
              <el-button type="primary" @click="handleAdd">添加商品</el-button>
            </div>
          </div>
        </template>

        <el-table :data="productStore.products" style="width: 100%" v-loading="productStore.loading">
          <el-table-column label="序号" width="80" type="index" />
          <el-table-column label="商品名称" prop="productName"> 
            <template #default="{ row }">
              <span class="product-name clickable" @click="goToProductDetail(row.id)">{{ row.name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="描述" prop="description">
            <template #default="{ row }">
              <el-tag size="small" type="info">{{ row.description }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="价格" prop="price" />
          <el-table-column label="库存" prop="stockQuantity" />
          <el-table-column label="图片">
            <template #default="{ row }">
              <img :src="row.imgUrl" alt="商品图片" class="product-image clickable" @click="goToProductDetail(row.id)" />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <el-button :icon="Edit" circle plain type="primary" @click="handleEdit(row)" />
              <el-button :icon="Delete" circle plain type="danger" @click="handleDelete(row)" />
            </template>
          </el-table-column>
          <template #empty>
            <el-empty description="暂无商品数据" />
          </template>
        </el-table>
      </el-card>
      <RtTabBar/>
    </div>

    <!-- 添加/编辑商品对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '添加商品' : '编辑商品'"
      width="500px"
    >
      <el-form :model="productForm" :rules="rules" ref="formRef" label-width="80px">
        <el-form-item label="商品名称" prop="productName"> 
          <el-input v-model="productForm.productName" placeholder="请输入商品名称" /> 
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="productForm.description" placeholder="请输入商品描述" />
        </el-form-item>
        <el-form-item label="价格" prop="price">
          <el-input v-model.number="productForm.price" placeholder="请输入商品价格" type="number" />
        </el-form-item>
        <el-form-item label="库存" prop="stockQuantity">
          <el-input v-model.number="productForm.stockQuantity" placeholder="请输入库存数量" type="number" />
        </el-form-item>
        <el-form-item label="图片">
          <el-upload
            ref="uploadRef"
            class="product-uploader"
            :show-file-list="false"
            :auto-upload="true"
            action="http://localhost:8080/upload"
            name="image"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :before-upload="beforeUpload"
          >
            <img v-if="productForm.imgUrl" :src="productForm.imgUrl" class="product-image-preview" />
            <el-icon v-else class="product-uploader-icon"><Plus /></el-icon>
          </el-upload>
          <div class="upload-buttons">
            <el-button type="primary" :icon="Plus" size="small" @click="triggerUpload">
              选择图片
            </el-button>
            <el-tooltip content="图片将在保存商品时一并提交" placement="top">
              <el-button type="info" :icon="InfoFilled" size="small">
                提示
              </el-button>
            </el-tooltip>
          </div>
          <div class="upload-tip">只能上传jpg/png文件，且不超过2MB</div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { Edit, Delete, Plus, InfoFilled } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useProductStore } from '@/stores/product';
import { useUserStore } from '@/stores/user';
import { useRouter } from 'vue-router';

const productStore = useProductStore();
const userStore = useUserStore();
const router = useRouter();
const dialogVisible = ref(false);
const dialogType = ref('add');
const formRef = ref(null);
const uploadRef = ref(null);

// 添加跳转到商品详情的方法
const goToProductDetail = (productId) => {
  router.push({ name: 'ProductDetail', params: { id: productId } });
};

// 表单数据
const productForm = ref({
  id: null,
  productName: '', // 修改为 productName
  description: '',
  price: 0,
  stockQuantity: 0,
  imgUrl: ''
});

// 表单验证规则
const rules = {
  productName: [ // 修改为 productName
    { required: true, message: '请输入商品名称', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入商品描述', trigger: 'blur' }
  ],
  price: [
    { required: true, message: '请输入商品价格', trigger: 'blur' },
    { type: 'number', message: '价格必须为数字值', trigger: 'blur' }
  ],
  stockQuantity: [
    { required: true, message: '请输入库存数量', trigger: 'blur' },
    { type: 'number', message: '库存数量必须为数字值', trigger: 'blur' }
  ]
};

// 添加商品
const handleAdd = () => {
  if (!userStore.checkLogin()) return;
  dialogType.value = 'add';
  productForm.value = {
    id: null,
    productName: '', // 修改为 productName
    description: '',
    price: 0,
    stockQuantity: 0,
    imgUrl: ''
  };
  dialogVisible.value = true;
};

// 编辑商品
const handleEdit = (row) => {
  if (!userStore.checkLogin()) return;
  dialogType.value = 'edit';
  productForm.value = {
    id: row.id,
    productName: row.name, 
    description: row.description,
    price: row.price,
    stockQuantity: row.stockQuantity,
    imgUrl: row.imgUrl || '' // 确保imgUrl有值，即使是空字符串
  };
  dialogVisible.value = true;
};

// 删除商品
const handleDelete = (row) => {
  if (!userStore.checkLogin()) return;
  ElMessageBox.confirm(
    `确定要删除商品"${row.name}"吗？`, // 使用 productName
    '温馨提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      const success = await productStore.deleteProduct(row.id);
      if (success) {
        ElMessage.success('删除成功');
      } else {
        ElMessage.error('删除失败');
      }
    } catch (error) {
      console.error('删除商品失败:', error);
      ElMessage.error('删除失败');
    }
  }).catch(() => {
    ElMessage.info('已取消删除');
  });
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async (valid) => {
    if (valid) {
      // 创建一个新对象，将imgUrl映射为imagePath
      const productData = {
        ...productForm.value,
        imagePath: productForm.value.imgUrl // 添加这一行，确保后端能接收到图片路径
      };
      
      console.log('提交的商品数据:', productData); // 添加日志，便于调试
      
      let success = false;
      if (dialogType.value === 'add') {
        success = await productStore.addProduct(productData);
      } else {
        success = await productStore.updateProduct(productForm.value.id, productData);
      }

      if (success) {
        dialogVisible.value = false;
      }
    }
  });
};

// 触发文件选择
const triggerUpload = () => {
  if (uploadRef.value) {
    uploadRef.value.$el.querySelector('input').click();
  }
};

// 上传图片成功时的处理
const handleUploadSuccess = (response) => {
  console.log('上传响应:', response);
  if (response.code === 0) {
    productForm.value.imgUrl = response.data;
    ElMessage.success('图片上传成功');
  } else {
    ElMessage.error(response.message || '上传失败');
  }
};

// 上传图片失败时的处理
const handleUploadError = (error) => {
  console.error('上传失败:', error);
  ElMessage.error('上传失败，请重试');
};

// 上传前验证
const beforeUpload = (file) => {
  console.log('准备上传文件:', file);
  const isImage = file.type.startsWith('image/');
  const isLt2M = file.size / 1024 / 1024 < 2;

  if (!isImage) {
    ElMessage.error('只能上传图片文件!');
    return false;
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!');
    return false;
  }
  return true;
};

onMounted(async () => {
  console.log('产品管理页面加载，当前token:', userStore.token);
  if (userStore.checkLogin()) {
    const success = await productStore.getProductList();
    if (!success) {
      console.log('获取商品列表失败，准备跳转到登录页');
      router.push('/login');
    }
  }
});
</script>

<style lang="scss" scoped>
.product-manage {
  min-height: 100vh;
  background: #f6f8fa;
}

/* 添加可点击样式 */
.clickable {
  cursor: pointer;
  color: #2193b0;
  
  &:hover {
    color: #1565c0;
    text-decoration: underline;
  }
}

.product-container {
  padding: 20px;
  margin-top: 20px;
}

.product-card {
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 2px 12px #e3f2fd;

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .title {
      font-size: 18px;
      font-weight: bold;
      color: #1565c0;
    }
  }
}

.product-name {
  font-weight: 500;
  color: #2193b0;
}

:deep(.el-table) {
  --el-table-border-color: #e3f2fd;
  --el-table-header-bg-color: #f8fafc;

  th {
    font-weight: bold;
    color: #1565c0;
  }
}

:deep(.el-button.is-circle) {
  margin: 0 4px;
}

.product-image {
  width: 50px;
  height: 50px;
  border-radius: 5px;
  
  &.clickable:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(33, 147, 176, 0.3);
    transition: all 0.3s ease;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

// 添加图片上传相关样式
.product-uploader {
  :deep() {
    .product-image-preview {
      width: 178px;
      height: 178px;
      display: block;
      object-fit: cover;
    }

    .el-upload {
      border: 1px dashed var(--el-border-color);
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: var(--el-transition-duration-fast);
      width: 178px;
      height: 178px;
    }

    .el-upload:hover {
      border-color: var(--el-color-primary);
    }

    .product-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 178px;
      height: 178px;
      text-align: center;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}

.upload-buttons {
  margin-top: 10px;
  display: flex;
  gap: 10px;
}

.upload-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}
</style>
