spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ****************************************
    username: root
    password: 123456
  data:
    redis:
      database: 0
      host: 127.0.0.1
      port: 6379
      password: 123456
      lettuce:
        pool:
          max-active: 100
          max-idle: 100
          min-idle: 0
          max-wait: -1
      timeout: 5000
  thymeleaf:
    cache: false
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
      enabled: true

mybatis:
  configuration:
    map-underscore-to-camel-case: true

mybatis-plus:
  type-aliases-package: com.itheima.springbootcd.pojo
  mapper-locations: classpath*:mapper/*.xml

# 阿里云OSS配置
aliyun:
  oss:
    endpoint: https://oss-cn-hangzhou.aliyuncs.com
    bucketName: hmleadnews0225