package com.itheima.springbootcd.pojo;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

//统一响应结果

@Data
@NoArgsConstructor
@AllArgsConstructor
public class Result<T> {
    private Integer code;
    private String message;
    private T data;
    private Integer total;

    public static <T> Result<T> success() {
        return new Result<>(0, "操作成功", null, null);
    }

    public static <T> Result<T> success(T data) {
        return new Result<>(0, "操作成功", data, null);
    }

    public static <T> Result<T> success(T data, Integer total) {
        return new Result<>(0, "操作成功", data, total);
    }

    public static <T> Result<T> error(String message) {
        return new Result<>(1, message, null, null);
    }
    public int isSuccess() {
        return 0;
    }
}

