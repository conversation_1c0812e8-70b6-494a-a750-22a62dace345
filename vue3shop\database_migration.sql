-- 权限管理系统数据库迁移脚本

-- 1. 为用户表添加角色字段
ALTER TABLE user ADD COLUMN role VARCHAR(20) DEFAULT 'CUSTOMER' COMMENT '用户角色(CUSTOMER-普通用户,MANUFACTURER-生产商,ADMIN-管理员)';
ALTER TABLE user ADD COLUMN manufacturer_id INT DEFAULT NULL COMMENT '生产商ID(仅当role为MANUFACTURER时有值)';

-- 2. 创建生产商表
CREATE TABLE IF NOT EXISTS manufacturer (
    manufacturer_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '生产商ID',
    manufacturer_name VARCHAR(100) NOT NULL COMMENT '生产商名称',
    description TEXT COMMENT '生产商描述',
    logo_url VARCHAR(255) COMMENT '生产商logo',
    contact_info VARCHAR(255) COMMENT '联系方式',
    address VARCHAR(255) COMMENT '地址',
    website VARCHAR(255) COMMENT '官网',
    status INT DEFAULT 0 COMMENT '状态(0-正常,1-禁用)',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    update_time DATETIME NOT NULL COMMENT '更新时间',
    create_user INT NOT NULL COMMENT '创建者用户ID',
    INDEX idx_status (status),
    INDEX idx_create_user (create_user),
    FOREIGN KEY (create_user) REFERENCES user(user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='生产商表';

-- 3. 为商品表添加生产商ID字段
ALTER TABLE product ADD COLUMN manufacturer_id INT DEFAULT NULL COMMENT '生产商ID';
ALTER TABLE product ADD INDEX idx_manufacturer_id (manufacturer_id);

-- 4. 添加外键约束（可选，根据需要决定是否添加）
-- ALTER TABLE product ADD FOREIGN KEY (manufacturer_id) REFERENCES manufacturer(manufacturer_id);
-- ALTER TABLE user ADD FOREIGN KEY (manufacturer_id) REFERENCES manufacturer(manufacturer_id);

-- 5. 插入示例数据

-- 插入管理员用户（密码为123456的MD5值）
INSERT INTO user (username, password, email, phone, register_time, status, role, nickname, address) 
VALUES ('admin', 'e10adc3949ba59abbe56e057f20f883e', '<EMAIL>', '13800000000', NOW(), 0, 'ADMIN', '管理员', '管理员地址')
ON DUPLICATE KEY UPDATE role = 'ADMIN';

-- 插入示例生产商
INSERT INTO manufacturer (manufacturer_name, description, logo_url, contact_info, address, website, status, create_time, update_time, create_user)
VALUES 
('华硕科技', '华硕是全球领先的3C解决方案提供商，致力于为个人和企业用户提供最具创新价值的产品及应用方案。', 'https://example.com/asus-logo.png', '************', '台湾台北市', 'https://www.asus.com.cn', 0, NOW(), NOW(), 1),
('微星科技', 'MSI微星科技为全球前三大主机板制造商，并跨足到显示卡、笔记本电脑、服务器、工业电脑、多媒体、通讯产品及车用电子等领域。', 'https://example.com/msi-logo.png', '************', '台湾新北市', 'https://cn.msi.com', 0, NOW(), NOW(), 1),
('技嘉科技', '技嘉科技是台湾的电脑硬件生产商，以主板和显卡为主要产品。', 'https://example.com/gigabyte-logo.png', '************', '台湾台北市', 'https://www.gigabyte.cn', 0, NOW(), NOW(), 1);

-- 6. 更新现有用户的角色（将所有现有用户设置为普通用户）
UPDATE user SET role = 'CUSTOMER' WHERE role IS NULL OR role = '';

-- 7. 创建生产商用户示例
INSERT INTO user (username, password, email, phone, register_time, status, role, nickname, address, manufacturer_id) 
VALUES 
('asus_manager', 'e10adc3949ba59abbe56e057f20f883e', '<EMAIL>', '13800000001', NOW(), 0, 'MANUFACTURER', '华硕管理员', '华硕公司地址', 1),
('msi_manager', 'e10adc3949ba59abbe56e057f20f883e', '<EMAIL>', '13800000002', NOW(), 0, 'MANUFACTURER', '微星管理员', '微星公司地址', 2)
ON DUPLICATE KEY UPDATE role = 'MANUFACTURER';

-- 8. 更新现有商品，关联到生产商（示例）
-- 假设现有商品ID 1-10 属于华硕，11-20 属于微星，21-30 属于技嘉
UPDATE product SET manufacturer_id = 1 WHERE product_id BETWEEN 1 AND 10;
UPDATE product SET manufacturer_id = 2 WHERE product_id BETWEEN 11 AND 20;
UPDATE product SET manufacturer_id = 3 WHERE product_id BETWEEN 21 AND 30;

-- 9. 创建索引优化查询性能
CREATE INDEX idx_user_role ON user(role);
CREATE INDEX idx_manufacturer_status ON manufacturer(status);
CREATE INDEX idx_product_manufacturer ON product(manufacturer_id);

-- 10. 查询验证数据
SELECT '=== 用户角色统计 ===' as info;
SELECT role, COUNT(*) as count FROM user GROUP BY role;

SELECT '=== 生产商列表 ===' as info;
SELECT manufacturer_id, manufacturer_name, status FROM manufacturer;

SELECT '=== 商品生产商关联统计 ===' as info;
SELECT 
    m.manufacturer_name,
    COUNT(p.product_id) as product_count
FROM manufacturer m
LEFT JOIN product p ON m.manufacturer_id = p.manufacturer_id
GROUP BY m.manufacturer_id, m.manufacturer_name;
