<template>
  <div class="category-page">
    <RtHeader title="商品分类" />
    <div class="category-container">
      <el-card class="category-card">
        <template #header>
          <div class="header">
            <span class="title">商品分类</span>
            <div class="extra">
              <el-button type="primary" @click="handleAdd">添加分类</el-button>
            </div>
          </div>
        </template>

        <!-- 搜索框 -->
        <div class="search-container">
          <el-input
            v-model="searchKeyword"
            placeholder="请输入分类名称搜索"
            clearable
            @clear="handleClearSearch"
            @keyup.enter="handleSearch"
            class="search-input"
          >
            <template #append>
              <el-button :icon="Search" @click="handleSearch" />
            </template>
          </el-input>
        </div>

        <el-table :data="categoryStore.categoryList" style="width: 100%" v-loading="categoryStore.loading">
          <el-table-column label="序号" width="80" type="index" />
          <el-table-column label="分类名称" prop="categoryName">
            <template #default="{ row }">
              <span class="category-name clickable" @click="goToCategoryDetail(row)">{{ row.categoryName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="描述" prop="description">
            <template #default="{ row }">
              <el-tag size="small" type="info">{{ row.description }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" prop="createTime" width="180" />
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <el-button :icon="Edit" circle plain type="primary" @click="handleEdit(row)" />
              <el-button :icon="Delete" circle plain type="danger" @click="handleDelete(row)" />
            </template>
          </el-table-column>
          <template #empty>
            <el-empty description="暂无分类数据" />
          </template>
        </el-table>
      </el-card>
    </div>

    <!-- 添加/编辑分类对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '添加分类' : '编辑分类'"
      width="500px"
    >
      <el-form :model="categoryForm" :rules="rules" ref="formRef" label-width="80px">
        <el-form-item label="分类名称" prop="categoryName">
          <el-input v-model="categoryForm.categoryName" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="categoryForm.description" placeholder="请输入分类描述" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <RtTabBar />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { Edit, Delete, Search } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import RtHeader from '../user/RtHeader.vue'
import { useCategoryStore } from '@/stores/category'
import { useUserStore } from '@/stores/user'
import { useRouter } from 'vue-router'

const categoryStore = useCategoryStore()
const userStore = useUserStore()
const router = useRouter()
const dialogVisible = ref(false)
const dialogType = ref('add')
const formRef = ref(null)
const searchKeyword = ref('')

// 表单数据
const categoryForm = ref({
  categoryName: '',
  description: ''
})

// 表单验证规则
const rules = {
  categoryName: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入分类描述', trigger: 'blur' }
  ]
}

// 添加分类
const handleAdd = () => {
  if (!userStore.checkLogin()) return
  dialogType.value = 'add'
  categoryForm.value = {
    categoryName: '',
    description: ''
  }
  dialogVisible.value = true
}

// 编辑分类
const handleEdit = (row) => {
  if (!userStore.checkLogin()) return
  dialogType.value = 'edit'
  categoryForm.value = {
    categoryId: row.categoryId,
    categoryName: row.categoryName,
    description: row.description
  }
  dialogVisible.value = true//控制对话框的显示与隐藏
}

// 删除分类
const handleDelete = (row) => {
  if (!userStore.checkLogin()) return
  ElMessageBox.confirm(
    `确定要删除分类"${row.categoryName}"吗？`,
    '温馨提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    await categoryStore.deleteCategory(row.categoryId)
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return//检查表单引用是否存在

  await formRef.value.validate(async (valid) => {
    if (valid) {
      let success = false
      if (dialogType.value === 'add') {
        success = await categoryStore.addCategory(categoryForm.value)
      } else {
        success = await categoryStore.updateCategory(
          categoryForm.value.categoryId,
          categoryForm.value
        )
      }

      if (success) {
        dialogVisible.value = false
      }
    }
  })
}

// 搜索分类
const handleSearch = async () => {
  if (!userStore.checkLogin()) return

  if (searchKeyword.value.trim()) {
    await categoryStore.searchCategories(searchKeyword.value.trim())
  } else {
    ElMessage.warning('请输入搜索关键词')
  }
}

// 清空搜索
const handleClearSearch = async () => {
  searchKeyword.value = ''
  await categoryStore.getCategoryList()
}

// 跳转到分类详情页
const goToCategoryDetail = (category) => {
  router.push({ name: 'CategoryDetail', params: { id: category.categoryId } })
}

onMounted(async () => {
  console.log('分类页面加载，当前token:', userStore.token)
  if (userStore.checkLogin()) {
    await categoryStore.getCategoryList()
  }
})
</script>

<style lang="scss" scoped>
.category-page {
  min-height: 100vh;
  background: #f6f8fa;
}

.category-container {
  padding: 20px;
  margin-top: 20px;
}

.category-card {
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 2px 12px #e3f2fd;

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .title {
      font-size: 18px;
      font-weight: bold;
      color: #1565c0;
    }
  }
}

.search-container {
  margin-bottom: 20px;

  .search-input {
    max-width: 400px;
  }
}

.category-name {
  font-weight: 500;
  color: #2193b0;

  &.clickable {
    cursor: pointer;
    transition: color 0.3s ease;

    &:hover {
      color: #1565c0;
      text-decoration: underline;
    }
  }
}

:deep(.el-table) {
  --el-table-border-color: #e3f2fd;
  --el-table-header-bg-color: #f8fafc;

  th {
    font-weight: bold;
    color: #1565c0;
  }
}

:deep(.el-button.is-circle) {
  margin: 0 4px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>