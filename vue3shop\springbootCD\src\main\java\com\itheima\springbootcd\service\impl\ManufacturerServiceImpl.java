package com.itheima.springbootcd.service.impl;

import com.itheima.springbootcd.mapper.ManufacturerMapper;
import com.itheima.springbootcd.pojo.Manufacturer;
import com.itheima.springbootcd.service.ManufacturerService;
import com.itheima.springbootcd.utils.ThreadLocalUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 生产商Service实现类
 */
@Service
public class ManufacturerServiceImpl implements ManufacturerService {
    
    @Autowired
    private ManufacturerMapper manufacturerMapper;
    
    @Override
    public List<Manufacturer> list() {
        return manufacturerMapper.list();
    }
    
    @Override
    public Manufacturer findById(Integer id) {
        return manufacturerMapper.findById(id);
    }
    
    @Override
    public void add(Manufacturer manufacturer) {
        Map<String, Object> map = ThreadLocalUtil.get();
        Integer userId = (Integer) map.get("id");
        
        if (userId == null) {
            throw new RuntimeException("用户未登录或登录已过期");
        }
        
        manufacturer.setCreateTime(LocalDateTime.now());
        manufacturer.setUpdateTime(LocalDateTime.now());
        manufacturer.setCreateUser(userId);
        manufacturer.setStatus(0); // 默认正常状态
        
        manufacturerMapper.add(manufacturer);
    }
    
    @Override
    public void update(Manufacturer manufacturer) {
        manufacturer.setUpdateTime(LocalDateTime.now());
        manufacturerMapper.update(manufacturer);
    }
    
    @Override
    public void deleteById(Integer id) {
        manufacturerMapper.deleteById(id);
    }
    
    @Override
    public Manufacturer findByUserId(Integer userId) {
        return manufacturerMapper.findByUserId(userId);
    }
}
