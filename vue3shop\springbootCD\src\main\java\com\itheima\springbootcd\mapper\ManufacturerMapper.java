package com.itheima.springbootcd.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.itheima.springbootcd.pojo.Manufacturer;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 生产商Mapper接口
 */
@Mapper
public interface ManufacturerMapper extends BaseMapper<Manufacturer> {
    
    /**
     * 获取所有生产商列表
     */
    @Select("SELECT * FROM manufacturer WHERE status = 0 ORDER BY create_time DESC")
    List<Manufacturer> list();
    
    /**
     * 根据ID查询生产商详情
     */
    @Select("SELECT * FROM manufacturer WHERE manufacturer_id = #{id}")
    Manufacturer findById(Integer id);
    
    /**
     * 添加生产商
     */
    @Insert("INSERT INTO manufacturer(manufacturer_name, description, logo_url, contact_info, address, website, status, create_time, update_time, create_user) " +
            "VALUES(#{manufacturerName}, #{description}, #{logoUrl}, #{contactInfo}, #{address}, #{website}, #{status}, #{createTime}, #{updateTime}, #{createUser})")
    @Options(useGeneratedKeys = true, keyProperty = "manufacturerId")
    void add(Manufacturer manufacturer);
    
    /**
     * 更新生产商信息
     */
    @Update("UPDATE manufacturer SET manufacturer_name=#{manufacturerName}, description=#{description}, " +
            "logo_url=#{logoUrl}, contact_info=#{contactInfo}, address=#{address}, website=#{website}, " +
            "update_time=#{updateTime} WHERE manufacturer_id=#{manufacturerId}")
    void update(Manufacturer manufacturer);
    
    /**
     * 删除生产商（软删除）
     */
    @Update("UPDATE manufacturer SET status = 1 WHERE manufacturer_id = #{id}")
    void deleteById(Integer id);
    
    /**
     * 根据用户ID查询生产商信息
     */
    @Select("SELECT * FROM manufacturer WHERE create_user = #{userId} AND status = 0")
    Manufacturer findByUserId(Integer userId);
}
