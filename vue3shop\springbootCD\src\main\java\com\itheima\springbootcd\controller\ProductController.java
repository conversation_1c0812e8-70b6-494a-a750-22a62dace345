package com.itheima.springbootcd.controller;

import com.itheima.springbootcd.pojo.Product;
import com.itheima.springbootcd.pojo.Result;
import com.itheima.springbootcd.service.IProductService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@RestController
@RequestMapping("/product")
public class ProductController {

    @Resource
    private IProductService productService;

    /**
     * 根据id查询
     * @param id
     * @return
     */
    @GetMapping("selectById/{id}")
    public Result<Product> selectById(@PathVariable("id") Integer id){
        Product entity=productService.selectById(id);
        return Result.success(entity);
    }

    /**
     * 获取列表
     * @return
     */
    @GetMapping("list")
    public Result<List<Product>> list() {
        return Result.success(productService.list());
    }

    /**
     * 新增
     * @param product
     * @return
     */
    @PostMapping("/add")
    public Result add(@RequestBody Product product) {
        // 检查产品名称是否已存在
        Product existingProduct = productService.findByProductName(product.getProductName());
        if (existingProduct == null) {
            // 产品名称不存在，可以添加
            productService.insert(product);
            return Result.success();
        } else {
            return Result.error("用户名已存在");
        }
    }

    /**
     * 更新
     * @param entity
     * @return
     */
    @PutMapping("update")
    public Result update(@RequestBody Product entity) {
        productService.updateById(entity);
        return Result.success();
    }

    /**
     * 批量删除
     * @param ids
     * @return
     */
    @DeleteMapping("delBatch")
    public Result delBatch(@RequestBody List<Integer> ids) {
        productService.removeByIds(ids);
        return Result.success();
    }

    @GetMapping("listByCategoryId/{categoryId}")
    public Result<List<Product>> listByCategoryId(@PathVariable("categoryId") Integer categoryId) {
        return Result.success(productService.listByCategoryId(categoryId));
    }
}
