package com.itheima.springbootcd.pojo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;


import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;

@Data
public class User implements Serializable {
    @NotNull
    private Integer userId;//用户id
    private String username;//用户名
    @JsonIgnore
    private String password;//密码
    @NotEmpty
    @Email
    private String email;//邮箱
    @NotEmpty
    private String phone;//电话
    private LocalDateTime registerTime;//注册时间
    private java.sql.Timestamp lastLoginTime;//最后一次登陆时间
    private Integer status;//用户状态(0-正常,1-禁用)
    private String avatar;//用户头像地址
    @NotEmpty
    private String address;//收货地址
    @NotEmpty
    @Pattern(regexp = "^\\S{1,10}$")
    private String nickname;//用户昵称
}
