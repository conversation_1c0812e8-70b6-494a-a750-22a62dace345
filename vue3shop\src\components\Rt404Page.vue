<template>
  <div class="not-found-container">
    <div class="mascot-container">
      <img src="@/assets/images/lingbao.gif" alt="404 Mascot" class="mascot-image" />
    </div>
    <h1>404</h1>
    <p>页面找不到了</p>
    <el-button type="primary" @click="goHome">返回首页</el-button>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const goHome = () => {
  router.push('/')
}
</script>

<style scoped>
.not-found-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  text-align: center;
}

.mascot-container {
  margin-bottom: 20px;
}

.mascot-image {
  width: 200px;
  height: auto;
}

.not-found-container h1 {
  font-size: 72px;
  margin-bottom: 20px;
  color: #409EFF;
}

.not-found-container p {
  font-size: 24px;
  margin-bottom: 30px;
  color: #606266;
}
</style>
